package com.ets.apply.application.app.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.bo.admin.ConfigNameKeyIntegerBO;
import com.ets.apply.application.common.bo.admin.ConfigNameKeyStringBO;
import com.ets.apply.application.common.dto.configBizField.*;
import com.ets.apply.application.common.dto.map.MapModuleAddPackageDTO;
import com.ets.apply.application.common.vo.configBizField.ConfigBizFieldListVO;
import com.ets.apply.application.common.vo.configBizField.ConfigBizFieldValuesListVO;
import com.ets.apply.application.infra.entity.ConfigBizFieldEntity;
import com.ets.apply.application.infra.entity.ConfigBizFieldValuesEntity;
import com.ets.apply.application.infra.entity.ProductPackageConfigEntity;
import com.ets.apply.application.infra.entity.map.MapPackageSnEntity;
import com.ets.apply.application.infra.service.*;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class ConfigBizFieldBusiness {
    @Autowired
    private ConfigBizFieldService configBizFieldService;
    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;
    /*
     * 获取全部模块列表
     */
    public IPage<ConfigBizFieldListVO> getAllList(ConfigBizFieldDTO dto) {
        // 分页设置
        IPage<ConfigBizFieldEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ConfigBizFieldEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.orderByDesc(ConfigBizFieldEntity::getSort);
        IPage<ConfigBizFieldEntity> pageList = configBizFieldService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            ConfigBizFieldListVO vo = new ConfigBizFieldListVO();
            BeanUtils.copyProperties(record, vo);
            return vo;
        });
    }

    /*
     * 获取全部模块列表
     */
    public IPage<ConfigBizFieldValuesListVO> getValueslist(ConfigBizFieldValuesDTO dto) {
        // 分页设置
        IPage<ConfigBizFieldValuesEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ConfigBizFieldValuesEntity::getBizField, dto.getBizField())
               .eq(ConfigBizFieldValuesEntity::getStatus, 1)
               .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        IPage<ConfigBizFieldValuesEntity> pageList = configBizFieldValuesService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            ConfigBizFieldValuesListVO vo = new ConfigBizFieldValuesListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setKey(record.getFieldKey());
            return vo;
        });
    }

    /*
     * 新增字段值
     */
    public Boolean addValues(ConfigBizFieldValuesAddDTO dto, String loginCode){
        //查找是否已存在
        if(configBizFieldValuesService.getInfoByBizFieldAndKey(dto.getBizField(),dto.getKey()) != null){
            ToolsHelper.throwException(dto.getKey()+"已存在");
        }
        ConfigBizFieldValuesEntity configBizFieldValues = new ConfigBizFieldValuesEntity();
        configBizFieldValues.setBizField(dto.getBizField());
        configBizFieldValues.setFieldKey(dto.getKey());
        configBizFieldValues.setName(dto.getName());
        configBizFieldValues.setSort(dto.getSort());
        configBizFieldValues.setOperator(loginCode);
        configBizFieldValues.setParentId(dto.getParentId());
        configBizFieldValuesService.create(configBizFieldValues);
        return true;
    }
    /*
     * 更新字段值
     */
    public Boolean modifyValues(ConfigBizFieldValuesModifyDTO dto, String loginCode){
        ConfigBizFieldValuesEntity configBizFieldValues = configBizFieldValuesService.getById(dto.getId());
        if(configBizFieldValues == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }
        ConfigBizFieldValuesEntity configBizFieldValuesCheck = configBizFieldValuesService.getInfoByBizFieldAndKey(configBizFieldValues.getBizField(),dto.getKey());
        if(configBizFieldValuesCheck != null && !configBizFieldValuesCheck.getId().equals(dto.getId())){
            ToolsHelper.throwException(dto.getKey()+"已存在");
        }

        LambdaUpdateWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ConfigBizFieldValuesEntity::getId, dto.getId())
                .set(ConfigBizFieldValuesEntity::getBizField, configBizFieldValues.getBizField())
                .set(ConfigBizFieldValuesEntity::getName, dto.getName())
                .set(ConfigBizFieldValuesEntity::getSort, dto.getSort())
                .set(ConfigBizFieldValuesEntity::getOperator, loginCode)
                .set(ConfigBizFieldValuesEntity::getParentId, dto.getParentId())
                .set(ConfigBizFieldValuesEntity::getFieldKey, dto.getKey());
        configBizFieldValuesService.updateByWrapper(wrapper);
        return true;
    }

    /*
     * 更新字段值
     */
    public Boolean delValues(ConfigBizFieldValuesDelDTO dto, String loginCode){
        ConfigBizFieldValuesEntity configBizFieldValues = configBizFieldValuesService.getById(dto.getId());
        if(configBizFieldValues == null){
            ToolsHelper.throwException(dto.getId()+"不存在数据");
        }
        LambdaUpdateWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ConfigBizFieldValuesEntity::getId, dto.getId())
                .set(ConfigBizFieldValuesEntity::getStatus,2)
                .set(ConfigBizFieldValuesEntity::getOperator, loginCode);
        configBizFieldValuesService.updateByWrapper(wrapper);
        return true;
    }


    /*
     * 获取配置的值对象  integer
     */
    public List<ConfigNameKeyIntegerBO> getIntegerBizField(String bizField) {
        List<ConfigNameKeyIntegerBO> bizFieldList = new ArrayList<>();
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
                .eq(ConfigBizFieldValuesEntity::getBizField, bizField)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        List<ConfigBizFieldValuesEntity> list =  configBizFieldValuesService.getListByWrapper(wrapper);
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                ConfigNameKeyIntegerBO configNameKeyInteger = new ConfigNameKeyIntegerBO();
                configNameKeyInteger.setKey(Integer.parseInt(configBizFieldValues.getFieldKey()));
                configNameKeyInteger.setName(configBizFieldValues.getName()+"【"+configBizFieldValues.getFieldKey()+"】");
                bizFieldList.add(configNameKeyInteger);
            }
        }
        return bizFieldList;
    }

    /*
     * 获取配置的值对象  integer
     */
    public List<ConfigNameKeyStringBO> getStringBizField(String bizField) {
        //获取数据类型
        ConfigBizFieldEntity configBizFieldEntity = configBizFieldService.getOneByColumn(bizField, ConfigBizFieldEntity::getBizField);
        if(configBizFieldEntity == null){
            return null;
        }
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
                .eq(ConfigBizFieldValuesEntity::getBizField, bizField)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        List<ConfigBizFieldValuesEntity> list =  configBizFieldValuesService.getListByWrapper(wrapper);
        List<ConfigNameKeyStringBO> bizFieldList = new ArrayList<>();
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                ConfigNameKeyStringBO configNameKeyString = new ConfigNameKeyStringBO();
                configNameKeyString.setKey(configBizFieldValues.getFieldKey());
                configNameKeyString.setName(configBizFieldValues.getName());
                bizFieldList.add(configNameKeyString);
            }
        }
        return bizFieldList;
    }


    /*
     * 获取全部模块列表
     */
    public Object getSelectObjectByBizField(String bizField) {
        // 查询条件设置
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ConfigBizFieldValuesEntity::getBizField, bizField)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        return null;

    }

    /*
     * 获取配置的值对象,int
     */
    public HashMap<Integer,String> getHashMapIntByBizField(String bizField) {
        HashMap<Integer,String> hashMap = new HashMap<>();
        //获取数据类型
        ConfigBizFieldEntity configBizFieldEntity = configBizFieldService.getOneByColumn(bizField, ConfigBizFieldEntity::getBizField);
        if(configBizFieldEntity == null){
            return hashMap;
        }
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
                .eq(ConfigBizFieldValuesEntity::getBizField, bizField)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        List<ConfigBizFieldValuesEntity> list =  configBizFieldValuesService.getListByWrapper(wrapper);
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                hashMap.put(Integer.parseInt(configBizFieldValues.getFieldKey()), configBizFieldValues.getName());
            }
        }
        return hashMap;
    }
}
