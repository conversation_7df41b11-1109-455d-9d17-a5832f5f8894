package com.ets.apply.application.common.dto.request.productPackage;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductPackageAddDTO {
    @NotNull(message = "名称不可为空")
    private String packageName;

    private BigDecimal packageFee;
    //原始价格
    private BigDecimal originalPrice;

    private BigDecimal tollDepositFee;

    @NotNull(message = "省份id不可为空")
    private Integer issuerId;

    private Integer isTruck = 0;
    private Integer isBase = 0;
    private Integer scene = 10;
    //营销配置
    private String saleSn;
    @NotNull(message = "goodsSku不可为空")
    private String goodsSku;
    @NotNull(message = "flowType不可为空")
    private Integer flowType;
    @NotNull(message = "cardId不可为空")
    private Integer cardId;
    @NotNull(message = "purchaseType不可为空")
    private Integer purchaseType;

    private Integer purchaseParty = 0;

    @NotNull(message = "设备类型不可为空")
    private Integer deviceType;

    @NotNull(message = "设备厂家不可为空")
    private Integer manufacturer;

    @NotNull(message = "业务归属不可为空")
    private Integer bizType;

    @NotNull(message = "发货类型不可为空")
    private Integer deliveryType;

    private Integer offlineAutoDelivery = 0;

    @NotNull(message = "激活视频不可为空")
    private Integer deviceTutorial;
    @NotNull(message = "设备名称不可为空")
    private String deviceName;

    private Integer deviceVersion;
    //颜色
    private Integer deviceColor;
    /*
     * 仓库发货配置
     */
    private Integer addressConfigId;
    /*
     *  渠道值
     */
    private String source;

    /**
     * 通行宝商品id
     */
    private String txbProductId;

    /*******************申办模块 start************************/
    @NotNull(message = "签约方式不可为空")
    private Integer paymentMode;
    private List<Integer> creditBank;
    //
    private Integer marketSource;

    // 保证金支付模式
    private Integer useDeposit = 0;


    /*****************申办模块 end**************************/

    /*******************营销模块 start************************/

    private Integer term = 0;

    private Integer truckTerm = 0;

    private Integer serviceBatch;

    /**
     * 产品包可用优惠券批次号
     */
//    private List<String> canUseCouponBatchNo;

//    /**
//     * 产品包可用优惠券类目s
//     */
//    private List<String> canUseJavaCouponCategory;

    /**
     * 回收金额
     */
    private BigDecimal recoveryAmount;

    /**
     * 是否支持设备估值 0 不支持 1支持 默认0
     */
    private Integer allowDeviceValuation = 0;

    /*****************营销模块 end**************************/

    /*******************售后模块 start************************/

    @NotNull(message = "是否收违约金不可为空")
    private Integer revokeNeedPay;
    //质保年限
    @NotNull(message = "质保年限不可为空")
    private Integer warrantyTime;
    //是否只有线下换货的
    private Integer isOnlyOfflineExchange = 0;
    //是否展示重办
    private Integer isShowReapply = 1;

    /*****************售后模块 end**************************/

    //是否使用模板id
    private Integer templateId = 0;

}
