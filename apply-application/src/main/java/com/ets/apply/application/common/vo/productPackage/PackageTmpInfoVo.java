package com.ets.apply.application.common.vo.productPackage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品套餐配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Data
public class PackageTmpInfoVo {


    /**
     * 商品套餐流水号
     */
    private String packageSn;

    /**
     * 设备费（元）
     */
    private BigDecimal packageFee;

    /**
     * 商品套餐名称
     */
    private String packageName;

    /**
     * 渠道码
     */
    private String source;

    /*
     * 状态 1-有效 2-无效
     */
    private Integer status;

    private String statusStr;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /*
     * 发布状态：1待发布2已发布
     */
    private Integer releaseStatus;

    private String releaseStatusStr;
    private Object applyConfig;
    private Object aftersaleConfig;
    private Object frontedConfig;
    private Object marketConfig;
    private Object pageConfig;

    private Integer bizType;

    private Integer isTruck;

    private Integer deliveryType;
    private Integer addressConfigId;

    private Integer isBase;
    private Integer templateId;

}
