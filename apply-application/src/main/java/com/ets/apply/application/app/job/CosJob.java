package com.ets.apply.application.app.job;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.business.job.CosSyncJobBusiness;
import com.ets.apply.application.common.dto.cosJob.CosSyncJobDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class CosJob {
    @Autowired
    private CosSyncJobBusiness cosSyncJobBusiness;
    /*
     *  轮询 未完成的任务管理
     */
    @XxlJob("cosSyncHandler")
    public ReturnT<String> cosSyncHandler(String params){
        CosSyncJobDTO dto = JSONObject.parseObject(params, CosSyncJobDTO.class);
        cosSyncJobBusiness.syncByDate(dto);
        return ReturnT.SUCCESS;
    }
}
