package com.ets.apply.application.app.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.factory.task.TaskFactory;
import com.ets.apply.application.app.thirdservice.feign.PayGatewayFeign;
import com.ets.apply.application.common.bo.pay.PayGateWayRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdSyncBO;
import com.ets.apply.application.common.consts.payment.PayGatewayConst;
import com.ets.apply.application.common.bo.pay.PrepayParamBO;
import com.ets.apply.application.common.config.queue.PayGatewayConfig;
import com.ets.apply.application.common.consts.payment.RefundStatusEnum;
import com.ets.apply.application.common.consts.productOrder.*;
import com.ets.apply.application.common.consts.redisCache.RedisCacheKeyConstants;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductOrderListDTO;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductPrepayDTO;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductRefundDTO;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.common.vo.channelProductOrder.ChannelProductOrderListVO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductOrderLogEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.ProductOrderLogService;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.apply.application.infra.service.ProductPackageService;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.util.NumberUtil;
import com.ets.user.feign.feign.UsersFeign;
import com.ets.user.feign.response.UsersResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ChannelProductOrderBusiness extends BaseBusiness {

    @Autowired
    private ProductOrderService productOrderService;
    @Autowired
    private ProductOrderLogService productOrderLogService;
    @Autowired
    private PayGatewayFeign payGatewayFeign;

    @Autowired
    protected PayGatewayConfig payGatewayConfig;

    @Resource(name = "com.ets.user.feign.feign.UsersFeign")
    private UsersFeign usersFeign;

    @Autowired
    private ProductPackageService productPackageService;

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate redisDefaultTemplate;

    @Autowired
    protected ProductOrderBusiness productOrderBusiness;
    @Autowired
    protected TaskRecordBusiness taskRecordBusiness;

    public IPage<ChannelProductOrderListVO> getChannelProductOrderList(ChannelProductOrderListDTO dto) {

        // 分页设置
        IPage<ProductOrderEntity> oPage = new Page<>(dto.getPageNum(), dto.getPageSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<ProductOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(StringUtils.isNotEmpty(dto.getPlateNo()), ProductOrderEntity::getPlateNo, dto.getPlateNo())
                .eq(NumberUtil.isPositive(dto.getOrderStatus()), ProductOrderEntity::getOrderStatus, dto.getOrderStatus())
                .eq(StringUtils.isNotEmpty(dto.getPackageSn()), ProductOrderEntity::getPackageSn, dto.getPackageSn())
                .eq(StringUtils.isNotEmpty(dto.getProductOrderSn()), ProductOrderEntity::getProductOrderSn, dto.getProductOrderSn())
                .eq(StringUtils.isNotEmpty(dto.getTransactionId()), ProductOrderEntity::getTransactionId, dto.getTransactionId())
                // 大于等于
                .ge(StringUtils.isNotEmpty(dto.getCreateTimeBegin()), ProductOrderEntity::getCreatedAt, dto.getCreateTimeBegin())
                // 小于
                .le(StringUtils.isNotEmpty(dto.getCreateTimeEnd()), ProductOrderEntity::getCreatedAt, dto.getCreateTimeEnd() + " 23:59:59")
                // 支付时间筛选
                .ge(StringUtils.isNotEmpty(dto.getPaidTimeBegin()), ProductOrderEntity::getPaidTime, dto.getPaidTimeBegin())
                .le(StringUtils.isNotEmpty(dto.getPaidTimeEnd()), ProductOrderEntity::getPaidTime, dto.getPaidTimeEnd() + " 23:59:59")
                .orderByDesc(ProductOrderEntity::getCreatedAt);
        if (NumberUtil.isPositive(dto.getChannelId().compareTo(ChannelIdEnum.DEFAULT.getCode()))) {
            wrapper.eq(ProductOrderEntity::getChannelId, dto.getChannelId());
        } else {
            // 后台使用直接过滤掉渠道是0 的订单
            wrapper.ne(ProductOrderEntity::getChannelId, ChannelIdEnum.DEFAULT.getCode());
        }

        IPage<ProductOrderEntity> pageList = productOrderService.getPageListByWrapper(oPage, wrapper);

        return pageList.convert(record -> {
            ChannelProductOrderListVO vo = new ChannelProductOrderListVO();
            BeanUtils.copyProperties(record, vo);
            vo.setPackageStr(getPackageName(record.getPackageSn()));
            return vo;
        });
    }


    /**
     * 获取产品包名称
     *
     * @param productOrderSn
     * @return
     */
    private String getPackageName(String productOrderSn) {
        Boolean hasKey = redisDefaultTemplate.hasKey(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME+productOrderSn);
        if (Boolean.TRUE.equals(hasKey)) {
            return redisDefaultTemplate.opsForValue().get(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME+productOrderSn);
        }
        String packageName = ProductPackageEnum.getDescByCode(productOrderSn);
        if (StringUtils.isEmpty(packageName)) {
            ProductPackageEntity productPackageEntity = productPackageService.getBySn(productOrderSn);
            if (productPackageEntity != null) {
                packageName = productPackageEntity.getPackageName();
            }
        }
        redisDefaultTemplate.opsForValue().set(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME+productOrderSn, packageName);
        redisDefaultTemplate.expire(RedisCacheKeyConstants.GD_APPLY_PRODUCT_PACKAGE_NAME+productOrderSn, 2, TimeUnit.HOURS);
        return packageName;
    }

    public JSONObject prepay(ChannelProductPrepayDTO dto) {
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(dto.getProductOrderSn(), ProductOrderEntity::getProductOrderSn);
        if (productOrderEntity == null) {
            ToolsHelper.throwException("商品订单不存在：" + dto.getProductOrderSn());
        }
        if (!dto.getUid().equals(productOrderEntity.getUid())) {
            ToolsHelper.throwException("您没有操作该订单权限：" + dto.getProductOrderSn());
        }
        if (!ChannelProductOrderStatusEnum.DEFAULT.getCode().equals(productOrderEntity.getOrderStatus())) {
            ToolsHelper.throwException("订单状态不正确");
        }
        // 获取用户信息
        String thirdCode = getUserThirdCode(productOrderEntity.getUid());
        // 获取商品信息，校验库存
        Integer mchType = getPackageMchType(productOrderEntity.getPackageSn());
        // 组装支付参数
        PrepayParamBO bo = new PrepayParamBO();
        bo.setOrderNo(productOrderEntity.getProductOrderSn());
        bo.setOrderType(PayGatewayConst.ORDER_TYPE);
        bo.setUserId(productOrderEntity.getUid());
        bo.setOpenId(thirdCode);
        bo.setPayAmount(productOrderEntity.getPaidAmount());
        bo.setPayWay(PayGatewayConst.PAY_WAY);
        bo.setMchType(mchType);
        bo.setProductName("商品订单支付: " + productOrderEntity.getProductOrderSn());
        bo.setTimeExpire(15);

        // 调支付服务 获取支付参数，创建支付单
        String ret = payGatewayFeign.prepay(bo);

        JsonResult<String> result = JsonResult.convertFromJsonStr(ret, String.class);
        result.checkError();
        // 返回支付参数
        return JSON.parseObject(result.getData());
    }

    private String getUserThirdCode(Long uid) {
        String thirdCode = "";
        UsersResponse user = usersFeign.getByUid(uid).getData();
        if (user != null) {
            thirdCode = user.getThirdCode();
        }
        if (thirdCode.equals("")) {
            ToolsHelper.throwException("用户信息获取失败，请联系客服");
        }
        return thirdCode;
    }

    private Integer getPackageMchType(String packageSn) {
        ProductPackageEntity productPackageEntity = productPackageService.getBySn(packageSn);
        if (productPackageEntity == null) {
            ToolsHelper.throwException("商品信息获取失败，请联系客服");
        }
        if (productPackageEntity.getThirdData().equals("")) {
            ToolsHelper.throwException("商品信息配置获取失败，请联系客服");
        }
        JSONObject thirdData = JSONObject.parseObject(productPackageEntity.getThirdData());

        if (thirdData.getString("mch_type") == null) {
            ToolsHelper.throwException("获取支付信息配置失败，请联系客服");
        }
        return Integer.valueOf(thirdData.getString("mch_type"));
    }

    /**
     * 接收处理支付回调消息
     *
     * @param body
     */
    public void PaySuccess(String body) {
        if (body == null) {
            return;
        }

        JSONObject result = JSON.parseObject(body);
        if (result.getInteger("status") != 2) {
            // 未支付成功则不处理
            return;
        }
        // 订单号
        String productOrderSn = result.getString("orderNo");
        // 支付单号
        String paymentSn = result.getString("paymentNo");
        // 支付时间
        String paidTime = result.getString("payTime");
        // 微信交易单号
        String transactionId = result.getString("transactionId");
        log.info("交易单号：" + transactionId);
        if (StringUtils.isEmpty(productOrderSn) || StringUtils.isEmpty(paymentSn)) {
            ToolsHelper.throwException("业务单号和支付单号不能为空");
        }
        // 获取订单信息
        ProductOrderEntity entity = productOrderService.getOneByColumn(productOrderSn, ProductOrderEntity::getProductOrderSn);
        if (entity == null) {
            ToolsHelper.throwException("记录不存在");
        }
        if (!entity.getOrderStatus().equals(ChannelProductOrderStatusEnum.DEFAULT.getCode())) {
            log.info("商品订单状态：" + entity.getOrderStatus() + " 不允许支付");
            return;
        }
        if (null == transactionId || transactionId.equals("")) {
            return;
        }
        productOrderService.channelPaySuccess(productOrderSn, paymentSn, paidTime, transactionId);
        createLog(ProductOrderLogTypeEnum.PAID.getCode(),
                entity,
                entity.getOrderStatus(),
                "订单已支付，支付系统单号：" + paymentSn,
                "系统");
        //支付成功通知第三方
        //吉利通知状态，通用版本 903101
//        if(entity.getSource().equals(SourceEnum.GEELY.getCode())){
//            //组装通知参数
//            productOrderBusiness.thirdOrderStatusNotify(entity,ProductOrderStatusEnum.PAID.getCode());
//        }
        //合作方订单同步统一入口,task
        if(entity.getReferType().equals(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())){
            taskRecordBusiness.addThirdPartnerOrderSyncTask(productOrderSn, "JavaPaySuccess", ProductOrderStatusEnum.PAID.getCode(), null);
        }

    }

    /**
     * 退款回调通知
     *
     * @param body
     */
    public void refundComplete(String body) {

        if (body == null) {
            return;
        }
        JSONObject result = JSON.parseObject(body);
        String refundNo = result.getString("refundNo");
        String productOrderSn = result.getString("orderNo");
        String refundAt = result.getString("refundTime");
        Integer status = result.getInteger("status");
        String refundFailMsg = result.getString("errCode") + " " + result.getString("errMsg");
        ProductOrderEntity productOrderEntity = getProductOrderBySn(productOrderSn);
        Integer orderStatus = ProductOrderStatusEnum.CLOSED.getCode();
        //判断是否需要退款状态类型
        if(!productOrderBusiness.checkIsGeelyType(productOrderEntity.getReferValue())){
            ChannelProductOrderStatusEnum channelProductOrderStatusEnum = RefundStatusEnum.of(status).toChannelProductOrderRefundStatusEnum();
            if (channelProductOrderStatusEnum.getCode().equals(productOrderEntity.getOrderStatus())) {
                log.info("渠道订单退款重复通知重复发起，不进行处理" + productOrderEntity.getProductOrderSn() + "refundComplete");
                return;
            }
            orderStatus = channelProductOrderStatusEnum.getCode();
        }
        if (refundAt.equals("")) {
            refundAt = null;
        }
        productOrderService.refundChannelProductOrder(productOrderSn, refundNo, orderStatus, refundAt);

        createLog(ProductOrderLogTypeEnum.REFUND_NOTIFY.getCode(),
                productOrderEntity,
                productOrderEntity.getOrderStatus(),
                "接收退款通知，退款单号：" + refundNo + refundFailMsg,
                "系统");

        //同步合作方订单
        if(productOrderEntity.getReferType().equals(ReferTypeEnum.THIRD_PARTNER_TYPE.getCode())){
            taskRecordBusiness.addThirdPartnerOrderSyncTask(productOrderEntity.getProductOrderSn(), "refundComplete", ProductOrderStatusEnum.CLOSED.getCode(), null);
        }

    }

    // 创建日志记录
    public void createLog(Integer logType, ProductOrderEntity entity, Integer afterStatus, String content, String operator) {

        ProductOrderLogEntity logEntity = new ProductOrderLogEntity();
        logEntity.setOperateUser(operator);
        logEntity.setProductOrderSn(entity.getProductOrderSn());
        logEntity.setLogType(logType);
        logEntity.setContent(content);
        logEntity.setAfterStatus(afterStatus);
        logEntity.setPreStatus(entity.getOrderStatus());
        logEntity.setLogisticStatus(entity.getLogisticStatus());

        productOrderLogService.create(logEntity);
    }

    public void refund(ChannelProductRefundDTO dto) {
        // 校验订单
        ProductOrderEntity productOrderEntity = getProductOrderBySn(dto.getProductOrderSn());
        if (!Arrays.asList(ChannelProductOrderStatusEnum.PAID.getCode(),
                ChannelProductOrderStatusEnum.REFUNDING.getCode(),
                ChannelProductOrderStatusEnum.REFUND_FAILED.getCode()
        ).contains(productOrderEntity.getOrderStatus())
        ) {
            ToolsHelper.throwException("订单状态当前不能进行该操作不能进行该操作");
        }
        // 组装mq 消息
        PayGateWayRefundBO payGateWayRefundBO = new PayGateWayRefundBO();
        payGateWayRefundBO.setPaymentNo(productOrderEntity.getPaymentSn());
        payGateWayRefundBO.setOrderNo(productOrderEntity.getProductOrderSn());
        payGateWayRefundBO.setAmount(productOrderEntity.getPaidAmount());
        payGateWayRefundBO.setUserId(productOrderEntity.getUid().intValue());
        payGateWayRefundBO.setOrderType(PayGatewayConst.ORDER_TYPE);
        payGateWayRefundBO.setReason(dto.getReason());
        // 发起mq
        try {
            Message sendMsg = new Message(payGatewayConfig.getRefundTopic(), payGatewayConfig.getChannelProductOrderRefundAutoTag(), JSON.toJSONString(payGateWayRefundBO).getBytes());
            payGatewayConfig.mqProducer().send(sendMsg);
        } catch (MQClientException e) {
            ToolsHelper.throwException("请求退款请求失败" + e.getErrorMessage());
        } catch (MQBrokerException e) {
            ToolsHelper.throwException("请求退款请求失败" + e.getErrorMessage());
        } catch (RemotingException | InterruptedException e) {
            ToolsHelper.throwException("请求退款请求失败" + e.getMessage());
        }
        productOrderService.refundingChannelProductOrder(productOrderEntity.getProductOrderSn());

        // 记录发起消息
        createLog(ProductOrderLogTypeEnum.REFUND.getCode(),
                productOrderEntity,
                productOrderEntity.getOrderStatus(),
                "发起退款操作：" + dto.getReason(),
                dto.getOperator());
    }

    public ProductOrderEntity getProductOrderBySn(String productOrderSn) {
        ProductOrderEntity entity = productOrderService.getOneByColumn(productOrderSn, ProductOrderEntity::getProductOrderSn);
        if (entity == null) {
            ToolsHelper.throwException("记录不存在");
        }

        return entity;
    }
}
