package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.cache.CacheKeyConstant;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.mapper.ProductPackageMapper;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 商品套餐配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Slf4j
@Service
@DS("db-apply")
public class ProductPackageService extends BaseService<ProductPackageMapper, ProductPackageEntity> {

    private static final String PRODUCT_PACKAGE_CACHE ="productPackage:cache";
    /**
     * 根据订单号获取订单信息
     *
     * @param packageSn 订单号
     * @return ProductPackageEntity
     */
    public ProductPackageEntity getBySn(String packageSn) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .eq(ProductPackageEntity::getPackageSn, packageSn)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }

    @Cacheable(value = CacheKeyConstant.PRODUCT_PACKAGE_ENTITY_CACHE +"#7200",key = "#packageSn")
    public ProductPackageEntity getBySnWithCache(String packageSn) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .eq(ProductPackageEntity::getPackageSn, packageSn)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }

    /**
     * 读取缓存，日期格式转化需要单独处理缓存，否则会报错，这里只读取信息避免日期格式处理
     * 缓存2小时
     */
    @Cacheable(value = PRODUCT_PACKAGE_CACHE+"#7200",key = "#packageSn")
    public String getPackageInfoBySnWithCache(String packageSn) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .select(ProductPackageEntity::getPackageInfo)
                .eq(ProductPackageEntity::getPackageSn, packageSn)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper).getPackageInfo();
    }

    /**
     * 获取所有的渠道产品包
     *
     * @return List<ProductPackageEntity>
     */
    public List<ProductPackageEntity> getAll(List<String> sourceList) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .select(ProductPackageEntity::getPackageSn,
                    ProductPackageEntity::getPackageName,
                    ProductPackageEntity::getSource,
                    ProductPackageEntity::getIsShowAdd
                )
                .in(ProductPackageEntity::getSource, sourceList)
                .orderByDesc(ProductPackageEntity::getSort)
                ;
        return super.baseMapper.selectList(wrapper);
    }

    /**
     * 获取库存预警列表
     * @param packageSnList 产品包列表
     * @return List<ProductPackageEntity>
     */
    public List<ProductPackageEntity> getStockAlarmList(List<String> packageSnList) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .eq(ProductPackageEntity::getStatus, 1)
                .gt(ProductPackageEntity::getAlarmStock, 0)
                .in(ObjectUtils.isNotEmpty(packageSnList), ProductPackageEntity::getPackageSn, packageSnList)
                .apply("package_stock < alarm_stock");
        return this.baseMapper.selectList(wrapper);
    }

    public List<ProductPackageEntity> getPackageNameByPackageSns(List packageSns) {
        QueryWrapper<ProductPackageEntity> wrapper = new QueryWrapper<>();
        wrapper.select("package_name,package_sn")
                .lambda()
                .in(ProductPackageEntity::getPackageSn, packageSns)
        ;

        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 批量查询产品包名字信息
     * @param packageSnList
     * @return
     */
    public List<ProductPackageEntity> batchQuery(List<String> packageSnList) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .select(ProductPackageEntity::getPackageSn, ProductPackageEntity::getPackageName)
                .in(ObjectUtils.isNotEmpty(packageSnList), ProductPackageEntity::getPackageSn, packageSnList);
        return this.baseMapper.selectList(wrapper);
    }


    /*
     *  更新库存数量
     */
    public void updateStock(String skuSn,Integer count) {
        try{
            LambdaUpdateWrapper<ProductPackageEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(ProductPackageEntity::getPackageSn, skuSn);
            lambdaUpdateWrapper.setSql("`package_stock` = `package_stock` " + (count > 0 ? "+" : "-") +" " + Math.abs(count));
            updateByWrapper(lambdaUpdateWrapper);
        }catch (Exception e){
            ToolsHelper.throwException("数据库操作失败，请检查库存的数量");
        }
    }

    /**
     * 根据排序和渠道获取产品包信息
     * @param sort
     * @param source
     * @return
     */
    public ProductPackageEntity getBySortAndSource(Integer sort, String source) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .eq(ProductPackageEntity::getSort, sort)
                .eq(ProductPackageEntity::getSource, source)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }


    /**
     * 获取基础包下的子包
     *
     * @return List<ProductPackageEntity>
     */
    public List<ProductPackageEntity> getListByParentPackageSn(String parentPackageSn) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .eq(ProductPackageEntity::getIsBase, 0)
                .eq(ProductPackageEntity::getParentPackageSn, parentPackageSn);
        return super.baseMapper.selectList(wrapper);
    }

    /**
     * 批量获取产品包名称
     */
    public List<ProductPackageEntity> getPackageNameBySns(List<String> packageSnList) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .select(ProductPackageEntity::getPackageName, ProductPackageEntity::getPackageSn)
                .in(ProductPackageEntity::getPackageSn, packageSnList);
        return super.baseMapper.selectList(wrapper);
    }

    /**
     * 批量获取产品包信息
     */
    public List<ProductPackageEntity> getPackageBySns(List<String> packageSnList) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .in(ProductPackageEntity::getPackageSn, packageSnList);
        return super.baseMapper.selectList(wrapper);
    }

    /**
     * 有顺序的获取产品包信息
     */
    public List<ProductPackageEntity> getPackageBySns(LinkedList<String> packageSnList) {
        Wrapper<ProductPackageEntity> wrapper = Wrappers.<ProductPackageEntity>lambdaQuery()
                .in(ProductPackageEntity::getPackageSn, packageSnList);
        return super.baseMapper.selectList(wrapper);
    }
}
