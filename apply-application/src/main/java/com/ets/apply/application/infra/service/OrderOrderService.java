package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.consts.order.*;
import com.ets.apply.application.common.consts.order.AftersaleStatus;
import com.ets.apply.application.common.consts.order.StatusEnum;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.mapper.OrderOrderMapper;
import com.ets.common.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-21
 */
@Slf4j
@Service
@DS("db-order-proxy")
public class OrderOrderService extends BaseService<OrderOrderMapper, OrderOrderEntity> {
    @Autowired
    OrderLogService orderLogService;
    public List<OrderOrderEntity> getOrdersByPlateNoAndColor(String plateNo, Integer plateColor){
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getPlateNo, plateNo)
                .eq(OrderOrderEntity::getPlateColor, plateColor)
                .orderByDesc(OrderOrderEntity::getCreatedAt);

        return super.baseMapper.selectList(wrapper);
    }

    /**
     * 获取排除当前订单的该车牌历史订单
     * @param orderSn
     * @param plateNo
     * @return
     */
    public List<OrderOrderEntity> getHistoryOrders(String orderSn, String plateNo, Long uid) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(OrderOrderEntity::getPlateNo, plateNo)
                .eq(NumberUtil.isPositive(uid), OrderOrderEntity::getUid, uid)
                .ne(OrderOrderEntity::getOrderSn, orderSn)
                .orderByDesc(OrderOrderEntity::getCreatedAt);

        return getListByWrapper(wrapper);
    }

    public List<OrderOrderEntity> getOrdersByPlateNo(String plateNo){
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getPlateNo, plateNo)
                .orderByDesc(OrderOrderEntity::getCreatedAt);

        return super.baseMapper.selectList(wrapper);
    }

    public List<OrderOrderEntity> getOrdersByPlateNoAndPhone(String plateNo, String phone){
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getPlateNo, plateNo)
                .eq(OrderOrderEntity::getSendPhone, phone)
                .orderByDesc(OrderOrderEntity::getCreatedAt);

        return super.baseMapper.selectList(wrapper);
    }

    public List<OrderOrderEntity> getOrdersByPhone(String phone){
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getSendPhone, phone)
                .orderByAsc(OrderOrderEntity::getStatus)
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("LIMIT 50");

        return super.baseMapper.selectList(wrapper);
    }

    public List<OrderOrderEntity> getOrdersByUid(Long uid){
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getUid, uid)
                .orderByAsc(OrderOrderEntity::getStatus)
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("LIMIT 30");

        return super.baseMapper.selectList(wrapper);
    }

    public List<OrderOrderEntity> getOrdersByUidList(List<Long> uids, Integer limit) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .in(OrderOrderEntity::getUid, uids)
                .orderByAsc(OrderOrderEntity::getStatus)
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("LIMIT " + limit);

        return getListByWrapper(wrapper);
    }

    public List<OrderOrderEntity> getOrderListByUidAndStatus(Long uid, List<Integer> orderStatus) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, orderStatus)
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("LIMIT 30");

        return super.baseMapper.selectList(wrapper);
    }

    /**
     * 根据订单号获取订单信息
     * @param orderSn 订单号
     * @return OrderOrderEntity
     */
    public OrderOrderEntity getByOrderSn(String orderSn) {

        return super.baseMapper.selectById(orderSn);
    }

    /**
     * 获取用户未取消的客车订单
     * @param uid
     * @param cardIds
     * @return
     */
    public List<OrderOrderEntity> getUserValidCarOrders(Long uid, List<Integer> cardIds) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getUid, uid)
                .notIn(OrderOrderEntity::getCardId, cardIds)
                .in(OrderOrderEntity::getStatus, Arrays.asList(StatusEnum.WAIT_FOR_PAY.getCode(), StatusEnum.WAIT_FOR_DELIVER.getCode(), StatusEnum.WAIT_FOR_RECEIVE.getCode(), StatusEnum.FINISH.getCode()));

        return super.baseMapper.selectList(wrapper);

    }

    /**
     * 通过车牌号码及颜色获取最新一条订单记录
     * @param plateNo:车牌号码
     * @param plateColor:车牌颜色
     * @return:订单对象
     */
    public OrderOrderEntity findOne(String plateNo, Integer plateColor) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getPlateNo, plateNo)
                .eq(OrderOrderEntity::getPlateColor, plateColor)
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("limit 1");

        return super.baseMapper.selectOne(wrapper);
    }

    /**
     * 通过订单号获取订单信息
     * @param orderSn:订单号
     * @return:订单对象
     */
    public OrderOrderEntity findByOrderSn(String orderSn) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getOrderSn, orderSn)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }

    /**
     * 通过产品包+状态+时间，统计数量
     */
    public Long countByPackageSn(String packageSn, List status, String startTime, String endTime) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
            .eq(OrderOrderEntity::getPackageSn, packageSn)
            .in(OrderOrderEntity::getStatus,status)
            .between(OrderOrderEntity::getCreatedAt,startTime,endTime);
        return super.baseMapper.selectCount(wrapper);
    }
    public OrderOrderEntity findByPlateNoAndUid(String plateNo, Integer plateColor, Long uid) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getPlateNo, plateNo)
                .eq(OrderOrderEntity::getPlateColor, plateColor)
                .eq(OrderOrderEntity::getUid, uid)
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("limit 1");

        return super.baseMapper.selectOne(wrapper);
    }

    public OrderOrderEntity getAddressInfo(String orderSn) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select(OrderOrderEntity::getSendName,OrderOrderEntity::getUid, OrderOrderEntity::getOrderSn, OrderOrderEntity::getStatus, OrderOrderEntity::getSendArea, OrderOrderEntity::getSendAddress, OrderOrderEntity::getSendPhone)
                .eq(OrderOrderEntity::getOrderSn, orderSn)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }

    /**
     * 获取用户申请中的订单或者激活过的订单（包含注销）
     * @param uid
     * @return
     */
    public List<OrderOrderEntity> getUserEffectOrderOrActivatedOrder(Long uid) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus,
                        Arrays.asList(StatusEnum.WAIT_FOR_DELIVER.getCode(), StatusEnum.WAIT_FOR_RECEIVE.getCode(), StatusEnum.FINISH.getCode()))
                .in(OrderOrderEntity::getAftersaleStatus,
                        Arrays.asList(AftersaleStatus.STATUS_NORMAL.getValue(),
                                AftersaleStatus.STATUS_APPLY.getValue(),
                                AftersaleStatus.STATUS_APPLY_CANCEL.getValue()
                        ));

        return super.baseMapper.selectList(wrapper);
    }


    /*
     * 更新售后状态
     */
    public Integer aftersaleStatusUpdate(OrderOrderEntity orderOrder, Integer afterSaleStatus, String reason){
        LambdaUpdateWrapper<OrderOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderOrderEntity::getOrderSn,orderOrder.getOrderSn())
                .set(OrderOrderEntity::getAftersaleStatus,afterSaleStatus);
        //日志记录
        orderLogService.addLog(orderOrder,reason);
        return this.updateByWrapper(wrapper);
    }

    public List<OrderOrderEntity> getHasPaidOrders(Long uid) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, Arrays.asList(
                        StatusEnum.WAIT_FOR_DELIVER.getCode(), StatusEnum.WAIT_FOR_RECEIVE.getCode(), StatusEnum.FINISH.getCode())
                );

        return getListByWrapper(wrapper);
    }

    public Boolean checkHasPaidOrders(Long uid) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, Arrays.asList(
                        StatusEnum.WAIT_FOR_DELIVER.getCode(), StatusEnum.WAIT_FOR_RECEIVE.getCode(), StatusEnum.FINISH.getCode())
                );

        OrderOrderEntity entity = getOneByWrapper(wrapper);

        return entity != null;
    }

    /**
     *  根据card_id 来确定是客车还是货车，货车的card_id 比较少
     *  获取用户未支付订单
     *  排除货车、0元单，
     *  不包含保证金、前装、银行类型订单
     */
    public OrderOrderEntity getUnpaidCarOrderWithConditions(Long uid, List<Integer> cardIds){

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getStatus, StatusEnum.WAIT_FOR_PAY.getCode())
                .eq(OrderOrderEntity::getUid,uid)
                .notIn(OrderOrderEntity::getCardId, cardIds)
                .eq(OrderOrderEntity::getDepositStatus, DepositStatusEnum.NONE.getValue())
                .eq(OrderOrderEntity::getIsFront, IsFrontEnum.DEFAULT.getCode())
                .ge(OrderOrderEntity::getNeedPay, 0)
                .in(OrderOrderEntity::getPurchaseParty, Arrays.asList(PurchasePartyEnum.bankDefault.getCode(),
                        PurchasePartyEnum.PURCHASE_PARTY_CAR_OWNER_SERVICE_WITHOUT_GIFT.getCode()))
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);

    }

    /**
     * 获取用户未支付货车订单:货车的card_id 比较少，用这个参数来做in查询
     */
    public OrderOrderEntity getUnpaidTruckOrderWithConditions(Long uid, List<Integer> cardIds){

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getStatus, StatusEnum.WAIT_FOR_PAY.getCode())
                .eq(OrderOrderEntity::getUid,uid)
                .in(OrderOrderEntity::getCardId, cardIds)
                .eq(OrderOrderEntity::getDepositStatus, DepositStatusEnum.NONE.getValue())
                .eq(OrderOrderEntity::getIsFront, IsFrontEnum.DEFAULT.getCode())
                .gt(OrderOrderEntity::getNeedPay, 0)
                .in(OrderOrderEntity::getPurchaseParty, Arrays.asList(PurchasePartyEnum.bankDefault.getCode(),
                        PurchasePartyEnum.PURCHASE_PARTY_CAR_OWNER_SERVICE_WITHOUT_GIFT.getCode()))
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);

    }

    /**
     * 获取客车待支付、已支付、已发货、已签收，并且未激活、未做售后的订单
     */
    public List<OrderOrderEntity> getUnfinishedOrderListByConditions(Long uid, List<Integer> cardIds,Integer limit) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, Arrays.asList(
                        StatusEnum.WAIT_FOR_PAY.getCode(),
                        StatusEnum.WAIT_FOR_DELIVER.getCode(),
                        StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                        StatusEnum.FINISH.getCode()
                ))
                .eq(OrderOrderEntity::getActivatedStatus, ActivatedStatusEnum.DEFAULT.getCode())
                .in(OrderOrderEntity::getAftersaleStatus, Arrays.asList(
                        AftersaleStatus.STATUS_NORMAL.getValue(),
                        AftersaleStatus.STATUS_APPLY.getValue()
                ))
                .notIn(OrderOrderEntity::getCardId, cardIds)
                .last("limit "+limit)
                .orderByDesc(OrderOrderEntity::getCreatedAt);
        return getListByWrapper(wrapper);

    }

    public OrderOrderEntity getLastApplyOrder(Long uid, String plateNo, Integer plateColor) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getUid, uid)
                .eq(OrderOrderEntity::getPlateNo, plateNo)
                .eq(OrderOrderEntity::getPlateColor, plateColor)
                .orderByAsc(OrderOrderEntity::getStatus)
                .orderByDesc(OrderOrderEntity::getCreatedAt);

        return getOneByWrapper(wrapper);
    }

    /**
     * 支付时间
     * 如果返回有问题，使用子查询再试试
     */
    public List<OrderOrderEntity> countByPaidTime(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OrderOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.select("package_sn,count(1) as total")
                .lambda()
                .between(OrderOrderEntity::getPaidAt, startTime, endTime)
                .groupBy(OrderOrderEntity::getPackageSn)
        ;
        return this.baseMapper.selectList(wrapper).stream().sorted(Comparator.comparing(OrderOrderEntity::getTotal).reversed()).collect(Collectors.toList());

    }

    /**
     * 获取支付时间段的订单，按照业务类型分组
     */
    public List<OrderOrderEntity> countByPaidTimeGroupByBizType(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OrderOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.select("biz_type,count(1) as total")
                .lambda()
                .between(OrderOrderEntity::getPaidAt, startTime, endTime)
                .groupBy(OrderOrderEntity::getBizType)
        ;
        return this.baseMapper.selectList(wrapper).stream().sorted(Comparator.comparing(OrderOrderEntity::getTotal).reversed()).collect(Collectors.toList());

    }

    /**
     * 代激活获取授权激活的订单，订单需要再处理筛选审核通过状态
     * 已激活的单也获取，用来给用户提示已激活
     */
    public List<OrderOrderEntity> getAuthorizeActivateOrders(Long uid) {
        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, Arrays.asList(
                        StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                        StatusEnum.FINISH.getCode()
                ))
                .in(OrderOrderEntity::getActivatedStatus, Arrays.asList(ActivatedStatusEnum.DEFAULT.getCode(),
                        ActivatedStatusEnum.ACTIVATED.getCode()
                ))
                .eq(OrderOrderEntity::getAftersaleStatus, AftersaleStatus.STATUS_NORMAL.getValue())
                .ne(OrderOrderEntity::getReviewOrderSn, "")
                .orderByDesc(OrderOrderEntity::getCreatedAt);
        return getListByWrapper(wrapper);
    }

    /**
     * 根据更新时间，返回订单号
     */
    public List<OrderOrderEntity> getOrderSnByUpdatedAt(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OrderOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.select("order_sn")
                .lambda()
                .between(OrderOrderEntity::getUpdatedAt, startTime, endTime)
        ;
        return this.baseMapper.selectList(wrapper);
    }
    /**
     * 根据更新时间，返回订单号
     */
    public List<OrderOrderEntity> getOrderSnByCreatedAt(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<OrderOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.select("order_sn")
                .lambda()
                .between(OrderOrderEntity::getCreatedAt, startTime, endTime)
        ;
        return this.baseMapper.selectList(wrapper);

    }

    /**
     * 根据创建时间，返回订单号 ，带分页
     */
    public IPage<OrderOrderEntity> getOrderSnByCreatedAtAndPage(LocalDateTime startTime, LocalDateTime endTime,Integer pageNum, Integer pageSize) {
        // 分页设置
        IPage<OrderOrderEntity> oPage = new Page<>(pageNum, pageSize, true);
        // 查询条件设置
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OrderOrderEntity::getOrderSn).between(OrderOrderEntity::getCreatedAt, startTime, endTime).orderByAsc(OrderOrderEntity::getCreatedAt);
        return this.getPageListByWrapper(oPage, wrapper);
    }

    public List<OrderOrderEntity> getOrderSnByCreatedAtByLimit(LocalDateTime startTime, LocalDateTime endTime, Integer limit){
        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .select(OrderOrderEntity::getOrderSn, OrderOrderEntity::getCreatedAt)
                .between(OrderOrderEntity::getCreatedAt, startTime, endTime)
                .orderByAsc(OrderOrderEntity::getCreatedAt)
                .last("limit "+limit);
        return super.baseMapper.selectList(wrapper);

    }

    /**
     * 根据创建时间，返回数量
     */
    public Long countOrderSnByCreatedAt(LocalDateTime startTime, LocalDateTime endTime) {
        // 查询条件设置
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(OrderOrderEntity::getCreatedAt, startTime, endTime);
        return (long) super.baseMapper.selectCount(wrapper);
    }

    /**
     * 获取用户最新的一条已支付0元信用卡订单
     * @param uid
     * @return
     */
    public OrderOrderEntity getLatestPaidCreditOrderByUidAndBizType(Long uid,List<Integer> bizType) {
        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getUid, uid)
                .eq(OrderOrderEntity::getPurchaseType, PurchaseTypeEnum.CreditCard.getCode())
                .eq(OrderOrderEntity::getStatus,StatusEnum.WAIT_FOR_DELIVER.getCode() )
                .eq(OrderOrderEntity::getNeedPay, 0)
                .in(OrderOrderEntity::getBizType, bizType)
                .orderByDesc(OrderOrderEntity::getCreatedAt);

        return getOneByWrapper(wrapper);
    }


    /**
     * 通过packageSn+uid获取,过滤已取消的
     */
    public List<OrderOrderEntity> getOrderListByPackageSnAndUid(Long uid,String packageSn) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getUid, uid)
                .eq(OrderOrderEntity::getPackageSn, packageSn)
                .in(OrderOrderEntity::getStatus, Arrays.asList(
                        StatusEnum.WAIT_FOR_DELIVER.getCode(),
                        StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                        StatusEnum.FINISH.getCode()
                ))
                .in(OrderOrderEntity::getAftersaleStatus, Arrays.asList(
                        AftersaleStatus.STATUS_NORMAL.getValue(),
                        AftersaleStatus.STATUS_APPLY.getValue()
                ))
                .orderByDesc(OrderOrderEntity::getCreatedAt);
        return getListByWrapper(wrapper);
    }

    /**
     * 判断当前uid是否存在有效的申办订单或激活成功
     */
    public List<OrderOrderEntity> getValidOrderListByUid(Long uid) {

        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, Arrays.asList(
                        StatusEnum.WAIT_FOR_DELIVER.getCode(),
                        StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                        StatusEnum.FINISH.getCode()
                ))
                .in(OrderOrderEntity::getAftersaleStatus, Arrays.asList(
                        AftersaleStatus.STATUS_NORMAL.getValue(),
                        AftersaleStatus.STATUS_APPLY.getValue()
                ))
                .in(OrderOrderEntity::getActivatedStatus, Arrays.asList(
                        ActivatedStatusEnum.DEFAULT.getCode(),
                        ActivatedStatusEnum.ACTIVATED.getCode()
                ))
                .orderByDesc(OrderOrderEntity::getCreatedAt);
        return getListByWrapper(wrapper);
    }

    /**
     * 获取uid进行中的订单
     */
    public List<OrderOrderEntity> getProcessingOrderListByUid(Long uid) {
        LambdaQueryWrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getUid, uid)
                .in(OrderOrderEntity::getStatus, Arrays.asList(
                        StatusEnum.WAIT_FOR_PAY.getCode(),
                        StatusEnum.WAIT_FOR_DELIVER.getCode(),
                        StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                        StatusEnum.FINISH.getCode()
                ))
                .eq(OrderOrderEntity::getActivatedStatus, ActivatedStatusEnum.DEFAULT.getCode());
        return getListByWrapper(wrapper);
    }

    /**
     * 根据创建时间，返回订单号 ，带分页
     * 只处理未激活的有效订单
     */
    public IPage<OrderOrderEntity> getOrderSnByCreatedAtAndPageOnce(LocalDateTime startTime, LocalDateTime endTime,Integer pageNum, Integer pageSize) {
        // 分页设置
        IPage<OrderOrderEntity> oPage = new Page<>(pageNum, pageSize, true);
        // 查询条件设置
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OrderOrderEntity::getOrderSn)
                .between(OrderOrderEntity::getCreatedAt, startTime, endTime)
                .orderByAsc(OrderOrderEntity::getCreatedAt);
        return this.getPageListByWrapper(oPage, wrapper);
    }

    /**
     * 获取用户激活未注销的数量
     */
    public Long countActivatedAndNotRevokeByUid(Long uid) {
        // 查询条件设置
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderOrderEntity::getUid, uid)
                .eq(OrderOrderEntity::getActivatedStatus, ActivatedStatusEnum.ACTIVATED.getCode())
                .eq(OrderOrderEntity::getAftersaleStatus, AftersaleStatus.STATUS_NORMAL.getValue());
        return super.baseMapper.selectCount(wrapper);
    }

    /**
     * 获取发货手机号下激活未注销的订单数量
     */
    public Long countActivatedAndNotRevokeBySendPhone(String sendPhone) {
        // 查询条件设置
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderOrderEntity::getSendPhone, sendPhone)
                .eq(OrderOrderEntity::getActivatedStatus, ActivatedStatusEnum.ACTIVATED.getCode())
                .eq(OrderOrderEntity::getAftersaleStatus, AftersaleStatus.STATUS_NORMAL.getValue());
        return super.baseMapper.selectCount(wrapper);
    }

    /**
     * 批量获取激活未注销的订单数量
     */
    public Long countActivatedAndNotRevokeByOrderSnList(List< String> orderSnList) {
        // 查询条件设置
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderOrderEntity::getOrderSn, orderSnList)
                .eq(OrderOrderEntity::getActivatedStatus, ActivatedStatusEnum.ACTIVATED.getCode())
                .eq(OrderOrderEntity::getAftersaleStatus, AftersaleStatus.STATUS_NORMAL.getValue());
        return super.baseMapper.selectCount(wrapper);
    }

    /**
     * 根据车牌+uid查询最新一笔订单
     */
    public OrderOrderEntity getOrdersByPlateNoAndUid(String plateNo, Long uid) {
        Wrapper<OrderOrderEntity> wrapper = Wrappers.<OrderOrderEntity>lambdaQuery()
                .eq(OrderOrderEntity::getPlateNo, plateNo)
                .eq(OrderOrderEntity::getUid, uid)
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("limit 1");

        return super.baseMapper.selectOne(wrapper);
    }

}
