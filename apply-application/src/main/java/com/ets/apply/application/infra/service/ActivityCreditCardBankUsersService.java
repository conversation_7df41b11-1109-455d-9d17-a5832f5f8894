package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.consts.activityCreditCardBankUser.*;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoClassifyEnum;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoWhichBankEnum;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.mapper.ActivityCreditCardBankUsersMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 银行给的办信用卡用户数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-09
 */
@Service
@DS("db-etc-proxy")
public class ActivityCreditCardBankUsersService extends BaseService<ActivityCreditCardBankUsersMapper, ActivityCreditCardBankUsersEntity> {

    /**
     * 只允许处理平安银行信用卡
     * 平安：当新户首刷成功后，记录该用户拥有领券资格
     *
     * @param updateTimeStart
     * @param updateTimeEnd
     * @return
     */
    public List<ActivityCreditCardBankUsersEntity> getPingAnWaitingRewardList(String updateTimeStart, String updateTimeEnd, List<Integer> classify) {

        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .in(ActivityCreditCardBankUsersEntity::getClassify, classify)
                .eq(ActivityCreditCardBankUsersEntity::getWhichBank, ActivityCreditCardUserInfoWhichBankEnum.PING_AN_INTERFACE.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getApplicationStatus, ActivityCreditCardBankUserApplyStatusEnum.APPLY_SUCCESS.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getIsNewUser, ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getIsFirstUse, ActivityCreditCardBankUserIsFirstUseEnum.FIRST_USE_YES.getCode())
                .gt(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeStart)
                .le(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeEnd)
                .last("limit 200");
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 交行：当新户激活后，记录该用户拥有领券资格-交通审核通过就是新户
     *
     * @param updateTimeStart
     * @param updateTimeEnd
     * @return
     */
    public List<ActivityCreditCardBankUsersEntity> getJiaoTongRewardList(String updateTimeStart, String updateTimeEnd, List<Integer> classify) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .in(ActivityCreditCardBankUsersEntity::getClassify, classify)
                .eq(ActivityCreditCardBankUsersEntity::getWhichBank, ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getApplicationStatus, ActivityCreditCardBankUserApplyStatusEnum.APPLY_SUCCESS.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getActivated, ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .gt(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeStart)
                .le(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeEnd)
                .last("limit 200");
        return this.baseMapper.selectList(wrapper);
    }


    /**
     * 获取中信银行激活新户订单数据
     *
     * @param updateTimeStart
     * @param updateTimeEnd
     * @return
     */
    public List<ActivityCreditCardBankUsersEntity> getCiticActivateList(String updateTimeStart, String updateTimeEnd) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .eq(ActivityCreditCardBankUsersEntity::getWhichBank, ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CITIC.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getApplicationStatus, ActivityCreditCardBankUserApplyStatusEnum.APPLY_SUCCESS.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getActivated, ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode())
                .eq(ActivityCreditCardBankUsersEntity::getIsNewUser, ActivityCreditCardBankUserIsNewUserEnum.NEW_USER_YES.getCode())
                .gt(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeStart)
                .le(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeEnd)
                .last("limit 200");
        return this.baseMapper.selectList(wrapper);
    }


    public ActivityCreditCardBankUsersEntity getOneByOrderSn(String orderSn) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .eq(ActivityCreditCardBankUsersEntity::getApplyNumber, orderSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 查找首刷并且首刷时间不是'1970-01-01' 的数据，-- 已首刷并且有首刷时间，未激活或者没有激活时间
     *
     * @param updateTimeStart
     * @param updateTimeEnd
     * @return
     */
    public List<ActivityCreditCardBankUsersEntity> getActivateCompensationData(LocalDateTime updateTimeStart, LocalDateTime updateTimeEnd) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .eq(ActivityCreditCardBankUsersEntity::getIsFirstUse, ActivityCreditCardBankUserIsFirstUseEnum.FIRST_USE_YES.getCode())
                .in(ActivityCreditCardBankUsersEntity::getWhichBank,Arrays.asList(
                        ActivityCreditCardUserInfoWhichBankEnum.PING_AN_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.GUANG_FA_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CITIC.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CMBC.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_SPD.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CEB.getCode()
                ))
                .isNotNull(ActivityCreditCardBankUsersEntity::getFirstUseTime)
                .ne(ActivityCreditCardBankUsersEntity::getClassify, ActivityCreditCardUserInfoClassifyEnum.TYPE_DEFAULT.getCode())
                .and(i -> i.eq(ActivityCreditCardBankUsersEntity::getActivated, ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_UNKNOWN.getCode())
                        .or().isNull(ActivityCreditCardBankUsersEntity::getActivateTime))
                .gt(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeStart)
                .le(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeEnd)
                .last("limit 1000");
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 查找已激活并且有激活时间的信用卡记录，审核状态是1 或者审核时间是空 的数据
     *
     * @param updateTimeStart
     * @param updateTimeEnd
     * @return
     */
    public List<ActivityCreditCardBankUsersEntity> getAuditCompensationData(LocalDateTime updateTimeStart, LocalDateTime updateTimeEnd) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .eq(ActivityCreditCardBankUsersEntity::getActivated, ActivityCreditCardBankUserActivatedEnum.STATUS_ACTIVATED_YES.getCode())
                .in(ActivityCreditCardBankUsersEntity::getWhichBank,Arrays.asList(
                        ActivityCreditCardUserInfoWhichBankEnum.PING_AN_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.GUANG_FA_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CITIC.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CMBC.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_SPD.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CEB.getCode()
                ))
                .isNotNull(ActivityCreditCardBankUsersEntity::getActivateTime)
                .ne(ActivityCreditCardBankUsersEntity::getClassify, ActivityCreditCardUserInfoClassifyEnum.TYPE_DEFAULT.getCode())
                .and(i -> i.eq(ActivityCreditCardBankUsersEntity::getStatus, ActivityCreditCardBankUserStatusEnum.STATUS_NOT_AUDIT.getCode())
                        .or().isNull(ActivityCreditCardBankUsersEntity::getAuditTime))
                .gt(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeStart)
                .le(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeEnd)
                .last("limit 1000");
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 获取需要进件时间补偿的数据
     *
     * @param updateTimeStart
     * @param updateTimeEnd
     * @return
     */
    public List<ActivityCreditCardBankUsersEntity> getSubmitCompensationData(LocalDateTime updateTimeStart, LocalDateTime updateTimeEnd) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .in(ActivityCreditCardBankUsersEntity::getStatus, Arrays.asList(
                        ActivityCreditCardBankUserStatusEnum.STATUS_FAIL_AUDIT.getCode(),
                        ActivityCreditCardBankUserStatusEnum.STATUS_PASS_AUDIT.getCode()
                ))
                .in(ActivityCreditCardBankUsersEntity::getWhichBank,Arrays.asList(
                        ActivityCreditCardUserInfoWhichBankEnum.PING_AN_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.GUANG_FA_INTERFACE.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.JIAO_TONG.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CITIC.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CMBC.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_SPD.getCode(),
                        ActivityCreditCardUserInfoWhichBankEnum.CREDIT_BANK_CEB.getCode()
                        ))
                .isNotNull(ActivityCreditCardBankUsersEntity::getAuditTime)
                .ne(ActivityCreditCardBankUsersEntity::getClassify, ActivityCreditCardUserInfoClassifyEnum.TYPE_DEFAULT.getCode())
                .and(i -> i.eq(ActivityCreditCardBankUsersEntity::getApplyCompleted, ActivityCreditCardBankUserApplyCompleteEnum.APPLY_DEFAULT.getCode())
                        .or().isNull(ActivityCreditCardBankUsersEntity::getSubmitTime))
                .gt(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeStart)
                .le(ActivityCreditCardBankUsersEntity::getUpdatedAt, updateTimeEnd)
                .last("limit 1000");
        return this.baseMapper.selectList(wrapper);
    }


    /**
     * 统计 进件数量
     * @return
     */
    public Long getCountByWhichBank(Integer whichBank) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .eq(ActivityCreditCardBankUsersEntity::getWhichBank, whichBank);
        return this.baseMapper.selectCount(wrapper);
    }

    /**
     * 统计 进件,审核状态
     * @return
     */
    public Long getCountByWhichBankAndStatus(Integer whichBank,Integer firstAuditStatus) {
        Wrapper<ActivityCreditCardBankUsersEntity> wrapper = Wrappers.<ActivityCreditCardBankUsersEntity>lambdaQuery()
                .eq(ActivityCreditCardBankUsersEntity::getWhichBank, whichBank)
                .eq(ActivityCreditCardBankUsersEntity::getFirstAuditStatus, firstAuditStatus);
        return this.baseMapper.selectCount(wrapper);
    }
}
