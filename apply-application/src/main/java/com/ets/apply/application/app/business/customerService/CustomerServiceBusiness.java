package com.ets.apply.application.app.business.customerService;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.common.dto.customerService.GetListByUidDTO;
import com.ets.apply.application.common.vo.customerService.GetListByUidVO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.relation.OrderBindLogisticOrderRelation;
import com.ets.apply.application.infra.relation.order.OrderOrderBindReviewOrderRelation;
import com.ets.apply.application.infra.service.LogisticOrderService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.apply.application.infra.service.ReviewOrderService;
import com.ets.common.ToolsHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CustomerServiceBusiness {

    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private ReviewOrderService reviewOrderService;

    @Autowired
    private LogisticOrderService logisticOrderService;

    public List<GetListByUidVO> getListByUid(GetListByUidDTO getListByUidDTO) {
        if (ObjectUtil.isEmpty(getListByUidDTO.getUid())) {
            ToolsHelper.throwException("uid不能为空");
        }
        List<GetListByUidVO> result = new ArrayList<>();

        // 查询的lambda表达式,有orderSn 就加上该查询条件
        LambdaQueryWrapper<OrderOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderOrderEntity::getUid, getListByUidDTO.getUid())
                .eq(StringUtils.isNotEmpty(getListByUidDTO.getOrderSn()), OrderOrderEntity::getOrderSn, getListByUidDTO.getOrderSn())
                .eq(StringUtils.isNotEmpty(getListByUidDTO.getPlateNo()), OrderOrderEntity::getPlateNo, getListByUidDTO.getPlateNo())
                .orderByDesc(OrderOrderEntity::getCreatedAt)
                .last("limit 100")
        ;

        // 获取结果
        List<OrderOrderEntity> orderOrderEntityList = orderOrderService.getListByWrapper(wrapper);

        // 如果结果空，直接返回空结果
        if (ObjectUtil.isEmpty(orderOrderEntityList)) {
            return result;
        }
        // 结果不是空，获取对应的关联审核单
        reviewOrderService.bindToMasterEntityList(orderOrderEntityList, OrderOrderBindReviewOrderRelation.class);
        // 绑定发货单
        logisticOrderService.bindToMasterEntityList(orderOrderEntityList, OrderBindLogisticOrderRelation.class);

        // 转换结果
        orderOrderEntityList.forEach(orderOrderEntity -> {
            GetListByUidVO getListByUidVO = new GetListByUidVO();
            BeanUtils.copyProperties(orderOrderEntity, getListByUidVO);

            // 审核数据
            if (ObjectUtil.isNotEmpty(orderOrderEntity.getReviewOrderEntity())) {
                GetListByUidVO.ReviewOrder reviewOrder = new GetListByUidVO.ReviewOrder();
                BeanUtils.copyProperties(orderOrderEntity.getReviewOrderEntity(), reviewOrder);
                getListByUidVO.setReviewOrder(reviewOrder);
            }
            // 发货数据
            if (ObjectUtil.isNotEmpty(orderOrderEntity.getLogisticOrderEntity())) {
                GetListByUidVO.LogisticOrder logisticOrder = new GetListByUidVO.LogisticOrder();
                BeanUtils.copyProperties(orderOrderEntity.getLogisticOrderEntity(), logisticOrder);
                getListByUidVO.setLogisticOrder(logisticOrder);
            }
            result.add(getListByUidVO);
        });

        return result;

    }
}
