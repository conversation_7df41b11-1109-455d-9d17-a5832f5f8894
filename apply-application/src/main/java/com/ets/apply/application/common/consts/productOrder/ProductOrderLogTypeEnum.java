package com.ets.apply.application.common.consts.productOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProductOrderLogTypeEnum {

    CREATED(1, "创建"),
    CANCEL(2, "取消"),
    REFUND(3, "退款"),
    REMARK(4, "备注"),
    SHIP(5, "发货"),
    BEGIN_AFTER_SALE(6, "发起售后"),
    CANCEL_AFTER_SALE(7, "取消售后"),
    FINISH_AFTER_SALE(8, "完成售后"),
    RECEIVED(9, "快递签收"),
    PUSH_ISSUER_ADMIN(10, "订单推送到审核发货平台"),
    ACTIVATED(11, "订单激活"),
    MODIFIED(12, "修改"),
    PAID(13, "支付"),
    REFUND_NOTIFY(14, "退款通知"),
    CHOOSE_BANK(15, "选择银行"),
    INVOICE(16, "开票"),
    INVOICE_REVERSAL(17, "开票冲红"),
    INVOICE_SMS(18, "开票短信"),
    ;

    private final Integer code;

    private final String description;

    public static String getDescByCode(Integer code) {
        for (ProductOrderLogTypeEnum node : ProductOrderLogTypeEnum.values()) {
            if (node.getCode().equals(code)) {
                return node.getDescription();
            }
        }

        return "";
    }

}