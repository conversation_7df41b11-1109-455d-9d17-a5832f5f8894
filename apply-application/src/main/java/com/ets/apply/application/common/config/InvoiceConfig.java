package com.ets.apply.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "params.invoice-config")
public class InvoiceConfig {

    /**
     * 指定source范围
     */
    private List<String> sourceList;

    /**
     * 没支付source范围
     */
    private List<String> notPaySourceList;

    /**
     * 后台开票source范围
     */
    private List<String> backendSourceList;
}
