package com.ets.apply.application.common.vo.productOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品订单开票信息VO
 */
@Data
public class ProductOrderInvoiceInfoVO {

    /**
     * 商品订单号
     */
    private String productOrderSn;

    /**
     * 订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paidTime;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 收货人
     */
    private String sendName;

    /**
     * 收货手机
     */
    private String sendPhone;

    /**
     * 开票链接
     */
    private String invoiceUrl;

    /**
     * 开票流水号
     */
    private String invoiceSn;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invoiceTime;

    /**
     * 是否已开票
     */
    private Boolean hasInvoice;

    /**
     * 是否允许开票
     */
    private Boolean allowInvoice;
}
