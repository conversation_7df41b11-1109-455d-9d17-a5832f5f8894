package com.ets.apply.application.app.business.job;
import com.ets.apply.application.common.config.CosConfig;
import com.ets.apply.application.common.dto.cosJob.CosDelJobDTO;
import com.ets.apply.application.common.dto.cosJob.CosSyncJobDTO;
import com.ets.apply.application.infra.entity.LogCosClearEntity;
import com.ets.apply.application.infra.service.LogCosClearService;
import com.ets.common.ToolsHelper;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.COSObjectSummary;
import com.qcloud.cos.model.DeleteObjectRequest;
import com.qcloud.cos.model.ListObjectsRequest;
import com.qcloud.cos.model.ObjectListing;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;


@Slf4j
@Component
public class CosSyncJobBusiness {
    @Autowired
    private CosConfig cosConfig;
    @Autowired
    private LogCosClearService logCosClearService;
    /*
     * 处理数据
     */
    public void cosSyncDeal(ListObjectsRequest listObjectsRequest){
        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);
        ObjectListing objectListing = null;
        do {
            try {
                objectListing = cosClient.listObjects(listObjectsRequest);
            } catch (CosServiceException e) {
                ToolsHelper.throwException(e.getMessage());
            } catch (CosClientException e) {
                ToolsHelper.throwException(e.getMessage());
            }
            // object summary 表示所有列出的 object 列表
            List<COSObjectSummary> cosObjectSummaries = objectListing.getObjectSummaries();
            log.info(listObjectsRequest.getPrefix()+" "+listObjectsRequest.getMarker()+" "+cosObjectSummaries.size());
            for (COSObjectSummary cosObjectSummary : cosObjectSummaries) {
                String[] keys = cosObjectSummary.getKey().split("/");
                if(keys.length  == 3){
                    LogCosClearEntity logCosClear = new LogCosClearEntity();
                    logCosClear.setCosCategory(keys[0]);
                    logCosClear.setCosDate(keys[1]);
                    logCosClear.setCosKey(keys[2]);
                    logCosClearService.create(logCosClear);
                }
            }

            String nextMarker = objectListing.getNextMarker();
            listObjectsRequest.setMarker(nextMarker);
        } while (objectListing.isTruncated());
    }
    /*
     *  按时间段处理
     */
    public Boolean syncByDate(CosSyncJobDTO dto){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(dto.getStartDate(), formatter);
        LocalDate endDate = LocalDate.parse(dto.getEndDate(), formatter);
        // 定义日期格式
        DateTimeFormatter formatterUse = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate currentDate = startDate;
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
        listObjectsRequest.setBucketName(dto.getBucketName());
        listObjectsRequest.setMarker(dto.getMarker());
        while (currentDate.isBefore(endDate) || currentDate.isEqual(endDate)) {
            listObjectsRequest.setPrefix(dto.getCosCategory() + "/" + currentDate.format(formatterUse));
            cosSyncDeal(listObjectsRequest);
            currentDate = currentDate.plusDays(1);
        }
        return true;
    }

    /*
     *  按时间段处理
     */
    public void delCos(CosDelJobDTO dto){

        // 1 初始化秘钥信息
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        // 3 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);
        try {
            DeleteObjectRequest deleteObjectRequest = new DeleteObjectRequest(dto.getBucketName(), dto.getKey());
            cosClient.deleteObject(deleteObjectRequest);
        } catch (CosServiceException e) {
            ToolsHelper.throwException(e.getMessage());
        } catch (CosClientException e) {
            ToolsHelper.throwException(e.getMessage());
        }
    }
}
