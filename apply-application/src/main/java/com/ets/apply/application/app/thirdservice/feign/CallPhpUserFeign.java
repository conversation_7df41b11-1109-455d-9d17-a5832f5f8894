package com.ets.apply.application.app.thirdservice.feign;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.fallback.CallPhpUserFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.CreateWarrantyDTO;
import com.ets.apply.application.app.thirdservice.request.user.GetByThirdTypeThirdCodeDTO;
import com.ets.apply.application.app.thirdservice.response.user.GetByThirdTypeThirdCodeVO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesCancelUserCardDTO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesOrderChangeDTO;
import com.ets.apply.application.common.dto.request.user.CheckQuotaDTO;
import com.ets.apply.application.common.dto.request.user.PlateNoCheckDuplicateDTO;
import com.ets.apply.application.common.dto.user.FindByUidDTO;
import com.ets.apply.application.common.dto.usercard.UserCardFindByOrderSnDTO;
import com.ets.apply.application.common.vo.usercard.UserCardFindByOrderSnVO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "${params.microUrls.gd-micro-user}",
        name = "CallPhpUserFeign",
        fallbackFactory = CallPhpUserFallbackFactory.class
)
public interface CallPhpUserFeign {

    /**
     * 根据车牌、车牌颜色查询是否已有卡签
     * @param dto
     * @return
     */
    @PostMapping(value = "/etc/etc/is-plate-duplicate")
    String checkPlateDuplicate(@RequestBody PlateNoCheckDuplicateDTO dto);

    @PostMapping(value = "/user-card/check-quota")
    String checkQuota(@RequestBody CheckQuotaDTO dto);

    @PostMapping(value = "/user-card/cancel-card")
    String cancelUserCard(@RequestBody OrderAftersalesCancelUserCardDTO dto);


    @PostMapping(value = "/user/find-by-uid")
    JsonResult<JSONObject> findByUid(@RequestBody FindByUidDTO dto);
    @PostMapping(value = "/user/update-user-apply-order-sn")
    String updateUserApplyOrderSn(@RequestBody OrderAftersalesCancelUserCardDTO dto);

    @PostMapping(value = "/warranty/create-warranty")
    String createWarranty(@RequestBody CreateWarrantyDTO dto);

    @PostMapping(value = "/user-real-name/find")
    JsonResult<JSONObject> findUserRealName(@RequestBody FindByUidDTO dto);
    @PostMapping(value = "/user-real-name/deleteRealName")
    JsonResult<JSONObject> deleteUserRealName(@RequestBody FindByUidDTO dto);

    @PostMapping(value = "/user-card/find-by-order-sn")
    JsonResult<UserCardFindByOrderSnVO> findByOrderSn(@RequestBody UserCardFindByOrderSnDTO dto);

    @PostMapping(value = "/auth-third/get-by-third-type-third-code")
    JsonResult<GetByThirdTypeThirdCodeVO> getByThirdTypeThirdCode(@RequestBody GetByThirdTypeThirdCodeDTO dto);

}
