package com.ets.apply.application.app.business;

import cn.hutool.extra.validation.BeanValidationResult;
import cn.hutool.extra.validation.ValidationUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.ets.apply.application.app.disposer.ProductOrderImportBatchDisposer;
import com.ets.apply.application.app.disposer.ProductOrderImportDisposer;
import com.ets.apply.application.common.bo.productOrder.ProductOrderImportBO;
import com.ets.apply.application.common.consts.importRecord.ImportBatchStatusEnum;
import com.ets.apply.application.common.consts.importRecord.ImportRecordStatusEnum;
import com.ets.apply.application.common.dto.productOrder.ProductOrderImportDTO;
import com.ets.apply.application.common.dto.request.productOrder.*;
import com.ets.apply.application.common.vo.importRecord.ImportBatchVO;
import com.ets.apply.application.common.vo.importRecord.ImportExcelVO;
import com.ets.apply.application.common.vo.productOrder.*;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.service.*;
import com.ets.common.*;
import com.ets.starter.queue.QueueDefault;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ProductOrderImportBusiness extends BaseBusiness {


    @Autowired
    private ImportBusiness importBusiness;

    @Autowired
    private QueueDefault queueDefault;

    @Autowired
    private ImportBatchService importBatchService;


    @Autowired
    private ImportRecordService importRecordService;

    @Autowired
    private ProductOrderBusiness productOrderBusiness;


    public ImportBatchVO importData(MultipartFile file, String loginCode) {

        ImportExcelVO<ProductOrderImportBO> importExcelVO = importBusiness.importExcel(file, ProductOrderImportBO.class, 1,1, 2, loginCode);

        ProductOrderImportDTO dto = new ProductOrderImportDTO();
        dto.setBatchNo(importExcelVO.getBatchEntity().getBathNo());
        dto.setLoginCode(loginCode);
        queueDefault.push(new ProductOrderImportBatchDisposer(dto));

        importBatchService.setDoing(importExcelVO.getBatchEntity().getBathNo());

        return BeanHelper.copy(ImportBatchVO.class, importExcelVO.getBatchEntity());
    }

    public void pushByRecord(ImportExcelVO<ProductOrderImportBO> importExcelVO, String loginCode) {

        int rowIndex = 1;
        for (ProductOrderImportBO productOrderImportBO : importExcelVO.getRows()) {

            trimBlank(productOrderImportBO);

            ProductOrderImportDTO dto = new ProductOrderImportDTO();
            dto.setImportBO(productOrderImportBO);
            dto.setBatchNo(importExcelVO.getBatchEntity().getBathNo());
            dto.setRowIndex(rowIndex);
            dto.setLoginCode(loginCode);

            queueDefault.push(new ProductOrderImportDisposer(dto));

            rowIndex++;
        }
    }

    public void trimBlank(ProductOrderImportBO bo) {

        if (bo.getThirdOrderSn() != null) bo.setThirdOrderSn(bo.getThirdOrderSn().trim());
        if (bo.getEtcPhone() != null) bo.setEtcPhone(bo.getEtcPhone().trim());
        if (bo.getPackageSn() != null) bo.setPackageSn(bo.getPackageSn().trim());
        if (bo.getSendAddress() != null) bo.setSendAddress(
                bo.getSendAddress().trim().replace(" ", "").replace(" ", "")
        );
        if (bo.getSendName() != null) bo.setSendName(bo.getSendName().trim());
        if (bo.getSendPhone() != null) bo.setSendPhone(bo.getSendPhone().trim());
        if (bo.getPaidAmount() != null) bo.setPaidAmount(bo.getPaidAmount().trim());
    }

    public List<ProductOrderImportRecordListVO> getAllByBatchNo(String batchNo) {

        List<ProductOrderImportRecordListVO> list = importBusiness.getAllByBatchNo(batchNo, ProductOrderImportRecordListVO.class, ProductOrderImportRecordListVO::setResult);

        if (list == null) {
            return null;
        }

        list.forEach(importRecordListVO -> {
            importRecordListVO.setPackageName(productOrderBusiness.getPackageName(importRecordListVO.getPackageSn()));
        });

        return list;
    }

    public ProductOrderImportProcessingVO getImportProcessing(String batchNo) {

        ImportBatchEntity batchEntity = importBusiness.getByBatchNo(batchNo, 1);

        List<ProductOrderImportRecordListVO> list = getAllByBatchNo(batchNo);

        int successCount = 0;
        int rowCount = 0;

        if (list != null) {
            for (ProductOrderImportRecordListVO vo : list) {
                if (vo.getResult() != null && vo.getResult().equals("创建成功")) {
                    successCount++;
                }
                // 已结束不可重试的才算, 已成功 或 非可重试的错误，且非待处理
                if (vo.getResult() == null || (! canRetryError(vo.getResult()) && ! vo.getResult().equals("待处理"))) {
                    rowCount++;
                }
            }
        }

        ProductOrderImportProcessingVO vo = new ProductOrderImportProcessingVO();
        vo.setList(list);

        vo.setSuccessCount(successCount);
        vo.setIsSuccess(successCount == batchEntity.getRecordCount());

        vo.setTotalCount(batchEntity.getRecordCount());

        vo.setIsFinished(batchEntity.getRecordCount() == rowCount);

        return vo;
    }

    public void exportRecord(String batchNo, HttpServletResponse response) {

        List<ProductOrderImportRecordListVO> exportList = getAllByBatchNo(batchNo);

        try {
            // 设置文本内省
            response.setContentType("application/vnd.ms-excel");
            // 设置字符编码
            response.setCharacterEncoding("utf-8");
            // 设置文件名
            String filename = URLEncoder.encode("商品订单批量导入处理结果" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + filename);
            EasyExcel.write(response.getOutputStream(), ProductOrderImportRecordListVO.class)
                    .sheet("商品订单批量导入处理结果")
                    .doWrite(exportList);

        } catch (IOException e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            ToolsHelper.throwException("导出文件失败");
        }
    }

    public void executeByImportRecord(ImportRecordEntity recordEntity, ProductOrderImportBO bo, String loginCode) {

        if (! Arrays.asList(
                ImportRecordStatusEnum.FAILED.getCode(),
                ImportRecordStatusEnum.DEFAULT.getCode()
        ).contains(recordEntity.getStatus())) {
            return;
        }

        // 创建商城订单
        try {

            ProductOrderCreateDTO createDTO = BeanHelper.copy(ProductOrderCreateDTO.class, bo);
            if (StringUtils.isEmpty(bo.getPaidAmount())) {
                createDTO.setPaidAmount(null);
            } else {
                createDTO.setPaidAmount(new BigDecimal(bo.getPaidAmount()));
            }

            validParam(createDTO);

            productOrderBusiness.createOrder(createDTO, loginCode);

            // 处理成功 更新状态
            importRecordService.success(recordEntity.getId());

        } catch (BizException bizException) {
            // 处理失败
            importRecordService.error(recordEntity.getId(), bizException.getMessage());
        } catch (Exception e) {
            // 处理失败
            importRecordService.error(recordEntity.getId(), "系统错误：" + e.getMessage());
        }
    }

    public void validParam(ProductOrderCreateDTO createDTO) {

        BeanValidationResult result  = ValidationUtil.warpValidate(createDTO);
        if (! result.isSuccess()) {
            String errorMsg = result.getErrorMessages().stream().map(BeanValidationResult.ErrorMessage::getMessage).collect(Collectors.joining(";"));

            ToolsHelper.throwException(errorMsg);
        }
    }

    public void executeBatch(String batchNo, String loginCode) {

        ImportBatchEntity batchEntity = importBatchService.getByBatchNo(batchNo);

        if (! Arrays.asList(
                ImportBatchStatusEnum.DOING.getCode(),
                ImportBatchStatusEnum.DEFAULT.getCode()
        ).contains(batchEntity.getStatus())) {
            return;
        }

        List<ProductOrderImportBO> list = JSON.parseArray(batchEntity.getContent(), ProductOrderImportBO.class);

        int rowIndex = 1;
        for(ProductOrderImportBO bo: list) {
            try {
                trimBlank(bo);

                ImportRecordEntity recordEntity = importBusiness.addImportRecord(bo, batchNo, rowIndex);

                executeByImportRecord(recordEntity, bo, loginCode);

                rowIndex++;
            } catch (Exception e) {
                log.info("批量导入处理失败：" + e.getMessage());
            }
        }

        // 更新状态
        if (checkImportBatchStatus(batchEntity).equals(ImportBatchStatusEnum.ALL_SUCCESS.getCode())) {
            importBatchService.allSuccess(batchEntity.getBathNo());
        }
        if (checkImportBatchStatus(batchEntity).equals(ImportBatchStatusEnum.PART_FAILED.getCode())) {
            importBatchService.partFailed(batchEntity.getBathNo());
        }
    }

    public void retryImportFailRecord(String batchNo, Integer rowIndex, String loginCode) {

        if (StringUtils.isEmpty(loginCode)) {
            return;
        }

        List<ImportRecordEntity> list = importRecordService.getUnSuccessByBatchNo(batchNo, rowIndex);

        for (ImportRecordEntity entity: list) {

            ProductOrderImportBO bo = JSON.parseObject(entity.getContent(), ProductOrderImportBO.class);

            executeByImportRecord(entity, bo, loginCode);
        }
    }

    public void checkImportBatch() {

        List<ImportBatchEntity> list = importBatchService.getUnFinishedList(1,10);
        if (list == null || list.isEmpty()) {
            return;
        }

        for (ImportBatchEntity entity : list) {

            retryImportFailRecord(entity.getBathNo(), null, entity.getLoginCode());

            // 更新状态
            if (checkImportBatchStatus(entity).equals(ImportBatchStatusEnum.ALL_SUCCESS.getCode())) {
                importBatchService.allSuccess(entity.getBathNo());
            }
            if (checkImportBatchStatus(entity).equals(ImportBatchStatusEnum.PART_FAILED.getCode())) {
                importBatchService.partFailed(entity.getBathNo());
            }
        }
    }

    public void retryFailedByBatchNo(String batchNo) {

        ImportBatchEntity entity = importBatchService.getByBatchNo(batchNo);
        if (entity == null) {
            ToolsHelper.throwException("导入批次不存在");
        }

        retryImportFailRecord(entity.getBathNo(), null, entity.getLoginCode());

        // 更新状态
        if (checkImportBatchStatus(entity).equals(ImportBatchStatusEnum.ALL_SUCCESS.getCode())) {
            importBatchService.allSuccess(entity.getBathNo());
        }
        if (checkImportBatchStatus(entity).equals(ImportBatchStatusEnum.PART_FAILED.getCode())) {
            importBatchService.partFailed(entity.getBathNo());
        }
    }

    public boolean canRetryError(String errorInfo) {

        return Arrays.asList(
                "收货地址解析错误：此key每秒请求量已达到上限",
                "收货地址解析错误：签名验证失败"
        ).contains(errorInfo);
    }

    public Integer checkImportBatchStatus(ImportBatchEntity entity) {

        List<ImportRecordEntity> rows = importRecordService.getAllByBatchNo(entity.getBathNo());

        int unFinishedCount = 0;
        int successCount = 0;
        for (ImportRecordEntity row: rows) {
            if (row.getStatus().equals(ImportRecordStatusEnum.DEFAULT.getCode())) {
                unFinishedCount++;
            }
            if (row.getStatus().equals(ImportRecordStatusEnum.FAILED.getCode())
                    && row.getErrorInfo() != null && canRetryError(row.getErrorInfo())
            ) {
                unFinishedCount++;
            }
            if (row.getStatus().equals(ImportRecordStatusEnum.SUCCESS.getCode())) {
                successCount++;
            }
        }

        if (successCount == entity.getRecordCount()) {
            return ImportBatchStatusEnum.ALL_SUCCESS.getCode();
        }

        if (rows.size() == entity.getRecordCount() && unFinishedCount == 0) {
            return ImportBatchStatusEnum.PART_FAILED.getCode();
        }

        return ImportBatchStatusEnum.DOING.getCode();
    }
}
