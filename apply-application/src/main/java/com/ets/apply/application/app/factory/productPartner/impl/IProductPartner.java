package com.ets.apply.application.app.factory.productPartner.impl;

import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdSyncBO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;

public interface IProductPartner {

    String refund(ProductOrderRefundBO productOrderRefundBO);

    void ship(ProductOrderShipBO productOrderShipBO);

    void orderSync(ProductOrderEntity productOrder, ProductOrderThirdSyncBO bo);
}
