package com.ets.apply.application.app.business;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.CallCenterMarketingFeign;
import com.ets.apply.application.common.consts.order.ActivatedStatusEnum;
import com.ets.apply.application.common.consts.order.AftersaleStatus;
import com.ets.apply.application.common.consts.order.DepositStatusEnum;
import com.ets.apply.application.common.consts.order.SceneEnum;
import com.ets.apply.application.common.consts.order.StatusEnum;
import com.ets.apply.application.common.consts.orderAftersales.OrderAftersalesGoodsStatusEnum;
import com.ets.apply.application.common.consts.productPackage.DeviceTypeEnum;
import com.ets.apply.application.common.vo.orderCenter.OrderCenterApplyOrderInfoVO;
import com.ets.apply.application.common.vo.orderCenter.OrderCenterApplyOrderListVO;
import com.ets.apply.application.infra.entity.OrderAftersalesApplyEntity;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.entity.ProductPackageEntity;
import com.ets.apply.application.infra.service.OrderAftersalesApplyService;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.ets.common.util.UserUtil;
import com.ets.pay.feign.feign.PayFeign;
import com.ets.pay.feign.response.PaymentOrderResponse;
import com.ets.user.feign.feign.UserDepositFeign;
import com.ets.user.feign.response.UserDepositVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import jakarta.annotation.Resource;


@Component
@Slf4j
public class OrderCenterBusiness {

    @Autowired
    OrderOrderService orderOrderService;

    @Autowired
    private RecoveryBusiness recoveryBusiness;

    @Autowired
    private OrderPackageBusiness orderPackageBusiness;

    @Resource(name = "com.ets.pay.feign.feign.PayFeign")
    PayFeign payFeign;

    @Resource(name = "com.ets.user.feign.feign.UserDepositFeign")
    UserDepositFeign userDepositFeign;

    @Autowired
    OrderBusiness orderBusiness;

    @Autowired
    OrderAftersalesApplyService orderAftersalesApplyService;

    @Autowired
    private CallCenterMarketingFeign callCenterMarketingFeign;
    public List<OrderCenterApplyOrderListVO> getApplyOrderList() {
        Long uid = UserUtil.getUid();

        // 获取订单状态（待发货、代签收、已完成）
        List<OrderOrderEntity> orderList = orderOrderService.getOrderListByUidAndStatus(uid, Arrays.asList(
                StatusEnum.WAIT_FOR_PAY.getCode(),
                StatusEnum.WAIT_FOR_DELIVER.getCode(),
                StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                StatusEnum.FINISH.getCode()
        ));

        // 有激活过的客车订单就是我司用户
        boolean hasActivatedOrder = isOurCompanyUser(orderList);

        List<OrderCenterApplyOrderListVO> listVo = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderList)) {
            orderList.stream().
                    filter(order -> {
                        // 如果不是待支付状态，不进行过滤，目的是过滤掉待支付且不符合支付条件的单,货车也进行过滤
                        if (Objects.equals(order.getStatus(), StatusEnum.WAIT_FOR_PAY.getCode())) {
                            // 非我司用户并且订单满足支付条件
                            return !hasActivatedOrder &&  orderBusiness.checkOrderCanBePaid(order);
                        }
                        return true;

                    })
                    .forEach(order -> {
                        // 产品包
                        ProductPackageEntity productPackage = orderPackageBusiness.getOrderProductPackage(order);

                        OrderCenterApplyOrderListVO vo = new OrderCenterApplyOrderListVO();
                        vo.setOrderSn(order.getOrderSn());

                        // 回收按钮
                        vo.setShowRecovery(recoveryBusiness.isAllowRecovery(order, productPackage));

                        // 客车货车
                        if (order.getScene() == SceneEnum.TRUCK.getCode()) {
                            vo.setBusinessType(2);
                            vo.setBusinessTypeStr("ETC办理（货车）");
                            if (order.getDepositStatus().equals(DepositStatusEnum.NONE.getValue())) {
                                // 通行保证金
                                vo.setDepositFee(order.getTollDepositFee());
                                vo.setDepositStatus(DepositStatusEnum.NONE.getValue());

                                if (order.getTollDepositFee().intValue() != 0) {
                                    UserDepositVO userDeposit = userDepositFeign.getOneByOrderSn(order.getOrderSn()).getData();
                                    if (ObjectUtils.isNotEmpty(userDeposit)) {
                                        // 注销、退款状态 保证金已退款
                                        if (Arrays.asList(3, 4).contains(userDeposit.getStatus())) {
                                            vo.setDepositStatus(DepositStatusEnum.REFUND.getValue());
                                        } else if (userDeposit.getStatus() == 1) {
                                            vo.setDepositStatus(DepositStatusEnum.USED.getValue());
                                        }
                                    }
                                }
                            } else {
                                // 保证金
                                vo.setDepositFee(order.getDepositFee());
                                vo.setDepositStatus(order.getDepositStatus());
                            }

                        } else {
                            vo.setBusinessType(1);
                            vo.setBusinessTypeStr("ETC办理（小汽车）");
                            vo.setDepositFee(order.getDepositFee());
                            vo.setDepositStatus(order.getDepositStatus());
                        }
                        vo.setOrderStatus(order.getStatus());
                        vo.setPlateNo(order.getPlateNo());
                        vo.setPlateColor(order.getPlateColor());
                        vo.setPaidAmount(order.getNeedPay());
                        // 支付时间的获取，优先获取订单自身的支付时间
                        LocalDateTime paidTime = getOrderPaidTime(order);
                        if (ObjectUtils.isNotEmpty(paidTime)) {
                            vo.setPaidTime(paidTime);
                        }
                        // 设备名称、图片
                        if (ObjectUtils.isNotEmpty(productPackage)) {
                            // 产品包获取设备类型
                            JSONObject jsonObject = productPackage.getPackageInfoObj();
                            if (ObjectUtils.isNotEmpty(jsonObject)) {
                                Integer deviceType = jsonObject.getInteger("device_type");
                                if (ObjectUtils.isNotEmpty(deviceType)) {
                                    vo.setDeviceType(deviceType);
                                }
                                String productImg = jsonObject.getString("product_img");
                                if (ObjectUtils.isNotEmpty(productImg)) {
                                    vo.setGoodsImgUrl(productImg);
                                }
                                String deviceName = jsonObject.getString("device_name");
                                if (ObjectUtils.isNotEmpty(deviceName)) {
                                    vo.setDeviceName(deviceName);
                                } else {
                                    vo.setDeviceName(DeviceTypeEnum.nameMap.getOrDefault(deviceType, "一代-经典ETC"));
                                }
                            }
                        } else {
                            vo.setDeviceType(0);
                            vo.setDeviceName("一代-经典ETC");
                        }
                        vo.setCreatedAt(order.getCreatedAt());
                        vo.setUpdatedAt(order.getUpdatedAt());
                        vo.setActivatedStatus(order.getActivatedStatus());
                        vo.setAftersaleStatus(order.getAftersaleStatus());
                        vo.setGoodsOrderSn(order.getGoodsOrderSn());
                        //售后状态判断
                        //是否已激活
                        if(order.getActivatedStatus()!= 0){
                            vo.setCanAftersaleIssuer(1);
                            vo.setCanAftersaleGoods(0);
                        }else{
                            if(
                               Arrays.asList(
                                    StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                                    StatusEnum.FINISH.getCode()
                               ).contains(order.getStatus())
                                       &&
                               StringUtils.isNotEmpty(order.getGoodsOrderSn())
                            ){
                                vo.setCanAftersaleGoods(1);
                                if(Arrays.asList(
                                        AftersaleStatus.STATUS_APPLY.getValue(),
                                        AftersaleStatus.STATUS_APPLY_FINISH.getValue()
                                ).contains(order.getAftersaleStatus())){
                                    OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getLatestByOrderSn(order.getOrderSn());
                                    //存在未完成的商品售后单
                                    if (orderAftersalesApply != null ) {
                                        if(orderAftersalesApply.getBusinessStatus() == 1){
                                            vo.setAftersaleGoodsStatus(OrderAftersalesGoodsStatusEnum.AFTERSALES_GOODS_STATUS_FINISH.getStatus());
                                        }else{
                                            vo.setAftersaleGoodsStatus(OrderAftersalesGoodsStatusEnum.AFTERSALES_GOODS_STATUS_PROCESSING.getStatus());
                                        }

                                        //订单做完仅取消或者退货退款完成状态的话
                                        if(
                                                orderAftersalesApply.getBusinessStatus() == 1 &&
                                                        order.getAftersaleStatus() == AftersaleStatus.STATUS_APPLY_FINISH.getValue()
                                        ){
                                            vo.setCanAftersaleGoods(0);
                                        }
                                    }else{
                                        //存在历史售后单
                                        vo.setCanAftersaleGoods(0);
                                    }


                                }
                            }
                        }
                        listVo.add(vo);
                    });
        }
        return listVo;
    }

    /**
     * 是否我司用户
     */
    public Boolean isOurCompanyUser(List<OrderOrderEntity> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }
        return orderList.stream().anyMatch(order -> Arrays.asList(
                ActivatedStatusEnum.ACTIVATED.getCode(),
                ActivatedStatusEnum.VOIDED.getCode()).contains(order.getActivatedStatus())
                && order.getScene() != SceneEnum.TRUCK.getCode()
        );
    }

    /**
     * 获取支付时间
     */
    public LocalDateTime getOrderPaidTime(OrderOrderEntity order) {
        // 订单中有支付时间，直接返回
        if (ObjectUtils.isNotEmpty(order.getPaidAt())) {
            return order.getPaidAt();
        }
        // 订单中有支付单，从支付单中获取
        if (ObjectUtil.isNotEmpty(order.getPaymentSn())) {
            PaymentOrderResponse paymentOrder = payFeign.getPaymentOrder(order.getPaymentSn()).getData();

            if (ObjectUtil.isNotEmpty(paymentOrder)) {
                return paymentOrder.getPaidAt();
            }
        }
        return null;
    }

    /*
     *  获取订单列表的操作参数
     */
    public OrderCenterApplyOrderInfoVO getOperateParamsByOrderSn(String orderSn) {
        OrderOrderEntity order = orderOrderService.getByOrderSn(orderSn);
        if( order == null){
            return null;
        }
        OrderCenterApplyOrderInfoVO vo = new OrderCenterApplyOrderInfoVO();

        // 产品包
        ProductPackageEntity productPackage = orderPackageBusiness.getOrderProductPackage(order);
        vo.setOrderSn(order.getOrderSn());

        // 回收按钮
        vo.setShowRecovery(recoveryBusiness.isAllowRecovery(order, productPackage));

        // 客车货车
        if (order.getScene() == SceneEnum.TRUCK.getCode()) {
            vo.setBusinessType(2);
            vo.setBusinessTypeStr("ETC办理（货车）");
            if (order.getDepositStatus().equals(DepositStatusEnum.NONE.getValue())) {
                // 通行保证金
                vo.setDepositFee(order.getTollDepositFee());
                vo.setDepositStatus(DepositStatusEnum.NONE.getValue());

                if (order.getTollDepositFee().intValue() != 0) {
                    UserDepositVO userDeposit =
                            userDepositFeign.getOneByOrderSn(order.getOrderSn()).getData();
                    if (userDeposit != null) {
                        // 注销、退款状态 保证金已退款
                        if (Arrays.asList(3, 4).contains(userDeposit.getStatus())) {
                            vo.setDepositStatus(DepositStatusEnum.REFUND.getValue());
                        } else if (userDeposit.getStatus() == 1) {
                            vo.setDepositStatus(DepositStatusEnum.USED.getValue());
                        }
                    }
                }
            } else {
                // 保证金
                vo.setDepositFee(order.getDepositFee());
                vo.setDepositStatus(order.getDepositStatus());
            }

        } else {
            vo.setBusinessType(1);
            vo.setBusinessTypeStr("ETC办理（小汽车）");
            vo.setDepositFee(order.getDepositFee());
            vo.setDepositStatus(order.getDepositStatus());
        }
        vo.setOrderStatus(order.getStatus());
        vo.setPlateNo(order.getPlateNo());
        vo.setPlateColor(order.getPlateColor());
        vo.setPaidAmount(order.getNeedPay());
        // 支付时间的获取，优先获取订单自身的支付时间
        LocalDateTime paidTime = getOrderPaidTime(order);
        if (ObjectUtils.isNotEmpty(paidTime)) {
            vo.setPaidTime(paidTime);
        }
        // 设备名称、图片
        if (ObjectUtils.isNotEmpty(productPackage)) {
            // 产品包获取设备类型
            JSONObject jsonObject = productPackage.getPackageInfoObj();
            if (ObjectUtils.isNotEmpty(jsonObject)) {
                Integer deviceType = jsonObject.getInteger("device_type");
                if (ObjectUtils.isNotEmpty(deviceType)) {
                    vo.setDeviceType(deviceType);
                }
                String productImg = jsonObject.getString("product_img");
                if (ObjectUtils.isNotEmpty(productImg)) {
                    vo.setGoodsImgUrl(productImg);
                }
                String deviceName = jsonObject.getString("device_name");
                if (ObjectUtils.isNotEmpty(deviceName)) {
                    vo.setDeviceName(deviceName);
                } else {
                    vo.setDeviceName(DeviceTypeEnum.nameMap.getOrDefault(deviceType, "一代-经典ETC"));
                }
            }
        } else {
            vo.setDeviceType(0);
            vo.setDeviceName("一代-经典ETC");
        }
        vo.setCreatedAt(order.getCreatedAt());
        vo.setUpdatedAt(order.getUpdatedAt());
        vo.setActivatedStatus(order.getActivatedStatus());
        vo.setAftersaleStatus(order.getAftersaleStatus());
        vo.setGoodsOrderSn(order.getGoodsOrderSn());
        //售后状态判断
        //是否已激活
        if(order.getActivatedStatus()!= 0){
            vo.setCanAftersaleIssuer(1);
            vo.setCanAftersaleGoods(0);
        }else {
            if (
                    Arrays.asList(
                            StatusEnum.WAIT_FOR_RECEIVE.getCode(),
                            StatusEnum.FINISH.getCode()
                    ).contains(order.getStatus())
                            && 
                    StringUtils.isNotEmpty(order.getGoodsOrderSn())
                    &&
                    order.getIsFront().equals(0) //前装订单不允许做售后
                    &&
                    !Arrays.asList(
                            35,
                           102
                    ).contains(order.getCardId()) //过滤出租车的card_id
            ) {
                vo.setCanAftersaleGoods(1);
                if (Arrays.asList(
                        AftersaleStatus.STATUS_APPLY.getValue(),
                        AftersaleStatus.STATUS_APPLY_FINISH.getValue()
                ).contains(order.getAftersaleStatus())) {
                    OrderAftersalesApplyEntity orderAftersalesApply = orderAftersalesApplyService.getLatestByOrderSn(order.getOrderSn());
                    //存在未完成的商品售后单
                    if (orderAftersalesApply != null) {
                        if (orderAftersalesApply.getBusinessStatus() == 1) {
                            vo.setAftersaleGoodsStatus(OrderAftersalesGoodsStatusEnum.AFTERSALES_GOODS_STATUS_FINISH.getStatus());
                            vo.setCanAftersaleGoods(0);
                        } else {
                            vo.setAftersaleGoodsStatus(OrderAftersalesGoodsStatusEnum.AFTERSALES_GOODS_STATUS_PROCESSING.getStatus());
                        }

                        //订单做完仅取消或者退货退款完成状态的话
                        if (
                                orderAftersalesApply.getBusinessStatus() == 1 &&
                                Objects.equals(order.getAftersaleStatus(), AftersaleStatus.STATUS_APPLY_FINISH.getValue())
                        ) {
                            vo.setCanAftersaleGoods(0);
                        }
                    } else {
                        //存在历史售后单
                        vo.setCanAftersaleGoods(0);
                    }


                }
            }
        }

        vo.setIsFront(order.getIsFront());
        //活动参与标识对象
        try{
            String etcActCheckJoinRet = callCenterMarketingFeign.etcActCheckJoin(Arrays.asList(order.getOrderSn()));
            JSONObject jsonObject = JSON.parseObject(etcActCheckJoinRet);
            vo.setActivity(jsonObject.getJSONObject("data"));
        }catch (Exception e){
            log.info("etcActCheckJoin 处理异常:"+e.getMessage());
        }

        return vo;
    }
}
