package com.ets.apply.application.controller.admin;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.ChannelProductOrderBusiness;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.business.ProductOrderImportBusiness;
import com.ets.apply.application.app.business.productOrder.ProductOrderInvoiceBusiness;
import com.ets.apply.application.common.config.LoginCodeLimitsConfig;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductOrderListDTO;
import com.ets.apply.application.common.dto.request.channelProductOrder.ChannelProductRefundDTO;
import com.ets.apply.application.common.dto.request.productOrder.ProductOrderCreateDTO;
import com.ets.apply.application.common.dto.request.productOrder.ProductOrderListDTO;
import com.ets.apply.application.common.dto.request.productOrder.ProductOrderLogListDTO;
import com.ets.apply.application.common.dto.request.productOrder.ProductOrderModifyDTO;
import com.ets.apply.application.common.vo.channelProductOrder.ChannelProductOrderListVO;
import com.ets.apply.application.common.vo.importRecord.ImportBatchVO;
import com.ets.apply.application.common.vo.productOrder.*;
import com.ets.apply.application.controller.BaseController;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.*;

@RequestMapping("/admin/productOrder")
@RestController
@RefreshScope
@Slf4j
public class AdminProductOrderController extends BaseController {
    @Autowired
    private LoginCodeLimitsConfig loginCodeLimitsConfig;
    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Autowired
    private ChannelProductOrderBusiness channelProductOrderBusiness;

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate redisDefaultTemplate;

    @Autowired
    private ProductOrderImportBusiness productOrderImportBusiness;

    @Autowired
    private ProductOrderInvoiceBusiness productOrderInvoiceBusiness;

    @RequestMapping("/create")
    public JsonResult<?> create(@Valid @RequestBody ProductOrderCreateDTO dto) {

        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");

        productOrderBusiness.createOrder(dto, loginCode);

        return JsonResult.ok();
    }

    @RequestMapping("/modify")
    public JsonResult<?> modify(@Valid @RequestBody ProductOrderModifyDTO dto) {

        productOrderBusiness.modify(dto);

        return JsonResult.ok();
    }


    @RequestMapping("/getList")
    @ResponseBody
    public JsonResult<IPage<ProductOrderListVO>> getList(@RequestBody(required = false) @Valid ProductOrderListDTO request) {

        if (request == null) {
            ToolsHelper.throwException("请传入JSON格式的参数");
        }
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");

        // 未传指定渠道 受限只能显示受限渠道
        if (StringUtils.isEmpty(request.getSource())) {
            List<String> sourceCodeList = new ArrayList<>();
            loginCodeLimitsConfig.getLimitSourceUserMap().forEach((k, v) -> {
                if (v.contains(loginCode)) {
                    sourceCodeList.add(k);
                }
            });
            request.setSourceList(sourceCodeList);
        } else {
            request.setSourceList(Collections.singletonList(request.getSource()));
        }

        return JsonResult.ok(productOrderBusiness.getList(request));
    }

    /**
     * 修改挂起状态接口
     * @param productOrderSn
     * @param isPending
     * @return
     */
    @PostMapping("/pending")
    public JsonResult<Object> pending(
            @RequestParam(value = "productOrderSn") String productOrderSn,
            @RequestParam(value = "isPending") Integer isPending
    ) {
        productOrderBusiness.pending(productOrderSn, isPending, RequestHelper.getAdminOperator());

        return JsonResult.ok();
    }

    @RequestMapping("/export")
    public JsonResult<Object> export(@RequestBody(required = false) @Valid ProductOrderListDTO request, HttpServletResponse response) {

        if (request == null) {
            ToolsHelper.throwException("请传入JSON格式的参数");
        }

        // 限制多线程并行操作
        String lockKey = "productOrderExport";
        if (! ToolsHelper.addLock(redisDefaultTemplate, lockKey, 120)) {
            ToolsHelper.throwException("正在导出中，请结束后再操作");
        }

        try {
            //是否受限用户
            String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
            // 未传指定渠道 受限只能显示受限渠道
            if (StringUtils.isEmpty(request.getSource())) {
                List<String> sourceCodeList = new ArrayList<>();
                loginCodeLimitsConfig.getLimitSourceUserMap().forEach((k, v) -> {
                    if (v.contains(loginCode)) {
                        sourceCodeList.add(k);
                    }
                });
                request.setSourceList(sourceCodeList);
            } else {
                request.setSourceList(Collections.singletonList(request.getSource()));
            }


            List<ProductOrderDownloadData> data = productOrderBusiness.getExportData(request);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            String fileName = "productOrders" + request.getCreateTimeBegin() + "--" + request.getCreateTimeEnd();
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ProductOrderDownloadData.class).sheet("模板").doWrite(data);

            ToolsHelper.unLock(redisDefaultTemplate, lockKey);

        } catch (Exception e) {

            ToolsHelper.unLock(redisDefaultTemplate, lockKey);

            if (e instanceof BizException) {
                throw (BizException) e;
            } else {
                ToolsHelper.throwException("导出失败：" + e.getMessage());
            }
        }

        return null;
    }

    @RequestMapping("/getSelectOptions")
    @ResponseBody
    public JsonResult<ProductOrderSelectOptionVO> getSelectOptions() {
        //是否受限用户
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");
        List<String> sourceCodeList = new ArrayList<>();
        loginCodeLimitsConfig.getLimitSourceUserMap().forEach((k, v) -> {
            if (v.contains(loginCode)) {
                sourceCodeList.add(k);
            }
        });
        return JsonResult.ok(productOrderBusiness.getSelectOptions(sourceCodeList));
    }

    @RequestMapping("/getLogList")
    @ResponseBody
    public JsonResult<IPage<ProductOrderLogListVO>> getLogList(@RequestBody(required = false) @Valid ProductOrderLogListDTO request) {

        if (request == null) {
            ToolsHelper.throwException("请传入JSON格式的参数");
        }

        return JsonResult.ok(productOrderBusiness.getLogList(request));
    }

    @PostMapping("/detail")
    @ResponseBody
    public JsonResult<ProductOrderDetailVO> detail(@RequestParam(value = "productOrderSn", required = false) String productOrderSn) {

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        return JsonResult.ok(productOrderBusiness.getDetail(productOrderSn));
    }

    @PostMapping("/remark")
    @ResponseBody
    public JsonResult<Object> remark(
            @RequestParam(value = "productOrderSn", required = false) String productOrderSn,
            @RequestParam(value = "remark", required = false) String remark
    ) {
        if (StringUtils.isEmpty(productOrderSn) || StringUtils.isEmpty(remark)) {
            ToolsHelper.throwException("参数不能为空");
        }

        productOrderBusiness.remark(productOrderSn, remark);

        return JsonResult.ok();
    }

    @PostMapping("/cancel")
    @ResponseBody
    public JsonResult<Object> cancel(
            @RequestParam(value = "productOrderSn", required = false) String productOrderSn,
            @RequestParam(value = "reason", required = false) String reason,
            @RequestParam(value = "dealApply", required = false) Integer dealApply
    ) {
        if (StringUtils.isEmpty(productOrderSn) || StringUtils.isEmpty(reason)) {
            ToolsHelper.throwException("参数不能为空");
        }

        productOrderBusiness.cancel(productOrderSn, reason,dealApply,RequestHelper.getAdminOperator());

        return JsonResult.ok();
    }

    @PostMapping("/refund")
    @ResponseBody
    public JsonResult<Object> refund(@RequestParam(value = "productOrderSn", required = false) String productOrderSn) {
        productOrderBusiness.refund(productOrderSn);

        return JsonResult.ok();
    }

    @PostMapping("/checkPlateNo")
    @ResponseBody
    public JsonResult<Object> checkPlateNo(@RequestParam(value = "plateNo", required = false) String plateNo) {
        productOrderBusiness.checkPlateNo(plateNo);

        return JsonResult.ok();
    }

    @PostMapping("/getLogisticInfo")
    @ResponseBody
    public JsonResult<JSONArray> getLogisticInfo(@RequestParam(value = "productOrderSn", required = false) String productOrderSn) {

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        return JsonResult.ok(productOrderBusiness.getLogisticInfo(productOrderSn));
    }

    @RequestMapping("/getGoodsInventoryLeft")
    @ResponseBody
    public JsonResult<Object> getGoodsInventoryLeft() {

        return JsonResult.ok(productOrderBusiness.getGoodsInventoryLeft());
    }


    @RequestMapping("/getChannelOrderList")
    @ResponseBody
    public JsonResult<IPage<ChannelProductOrderListVO>> getChannelOrderList(@RequestBody(required = false) @Valid ChannelProductOrderListDTO request) {

        if (request == null) {
            ToolsHelper.throwException("请传入JSON格式的参数");
        }

        return JsonResult.ok(channelProductOrderBusiness.getChannelProductOrderList(request));
    }


    @RequestMapping("/getChannelSelectOptions")
    @ResponseBody
    public JsonResult<HashMap<String, List<Map<String, String>>>> getChannelSelectOptions() {

        return JsonResult.ok(productOrderBusiness.getChannelSelectOptions());
    }

    @PostMapping("/refundChannelOrder")
    @ResponseBody
    public JsonResult<Object> refundChannelOrder(@RequestBody(required = false) @Valid ChannelProductRefundDTO request) {
        request.setOperator(RequestHelper.getAdminOperator());
        channelProductOrderBusiness.refund(request);
        return JsonResult.ok();
    }

    @PostMapping("/importData")
    public JsonResult<ImportBatchVO> importData(
            @RequestParam("file") MultipartFile file
    ) {

        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");

        ImportBatchVO vo = productOrderImportBusiness.importData(file, loginCode);

        return JsonResult.ok(vo);
    }

    @PostMapping("/getImportProcessing")
    public JsonResult<ProductOrderImportProcessingVO> getImportProcessing(
            @RequestParam(value = "batchNo", required = false) String batchNo
    ) {

        return JsonResult.ok(productOrderImportBusiness.getImportProcessing(batchNo));
    }

    @RequestMapping("/exportRecord")
    public void exportRecord(@RequestParam("batchNo") String batchNo, HttpServletResponse response) {

        productOrderImportBusiness.exportRecord(batchNo, response);
    }

    @PostMapping("/retryImportFailRecord")
    public JsonResult<Object> retryImportFailRecord(
            @RequestParam("batchNo") String batchNo,
            @RequestParam(value = "rowIndex", required = false) Integer rowIndex
    ) {
        String loginCode = RequestHelper.getHttpServletRequest().getHeader("loginCode");

        productOrderImportBusiness.retryImportFailRecord(batchNo, rowIndex, loginCode);

        return JsonResult.ok();
    }

    /**
     * 强制取消关单
     */
    @PostMapping("/forceCancel")
    @ResponseBody
    public JsonResult<Object> forceCancel(
            @RequestParam(value = "productOrderSn", required = false) String productOrderSn,
            @RequestParam(value = "reason", required = false) String reason,
            @RequestParam(value = "dealApply", required = false) Integer dealApply
    ) {
        if (StringUtils.isEmpty(productOrderSn) || StringUtils.isEmpty(reason)) {
            ToolsHelper.throwException("参数不能为空");
        }

        productOrderBusiness.forceCancel(productOrderSn, reason, dealApply,RequestHelper.getAdminOperator());
        return JsonResult.ok();
    }


    @PostMapping("/get-invoice-info")
    public JsonResult<Void> getInvoiceInfo(@RequestParam("productOrderSn") String productOrderSn) {
        return JsonResult.ok();
    }

    @PostMapping("/invoice")
    public JsonResult<Void> invoice(@RequestParam("productOrderSn") String productOrderSn) {
        return JsonResult.ok();
    }

    @PostMapping("/invoice-reversal")
    public JsonResult<Void> invoiceReversal(@RequestParam("productOrderSn") String productOrderSn) {
        return JsonResult.ok();
    }

    @PostMapping("/invoice-sms")
    public JsonResult<Void> invoiceSms(@RequestParam("productOrderSn") String productOrderSn) {
        return JsonResult.ok();
    }

}
