package com.ets.apply.application.infra.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * OCR识别记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ocr_result_record")
public class OcrResultRecordEntity extends BaseEntity<OcrResultRecordEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String recordSn;

    private Integer uid;

    /**
     * 图片类型
     */
    private String imgType;

    /**
     * 图片URL
     */
    private String imgUrl;

    /**
     * 图片内容摘要（md5+sha1）前端传
     */
    private String digest;

    /**
     * 识别结果
     */
    private String resultPlain;

    /**
     * 识别结果（加密）
     */
    private String result;

    /**
     * 用于后续数据同步，业务中禁止使用
     */
    private Integer id;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


}
