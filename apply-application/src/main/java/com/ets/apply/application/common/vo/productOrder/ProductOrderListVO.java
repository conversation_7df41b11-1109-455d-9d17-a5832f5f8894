package com.ets.apply.application.common.vo.productOrder;

import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.consts.productOrder.ProductPackageEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

@Data
public class ProductOrderListVO {

    private Integer id;

    /**
     * 商品订单号
     */
    private String productOrderSn;

    /**
     * 第三方订单号
     */
    private String thirdOrderSn;

    /**
     * 产品包sn
     */
    private String packageSn;

    /**
     * 申办单号
     */
    private String applyOrderSn;

    /**
     * 快递公司
     */
    private String logisticCompany;

    /**
     * 快递单号
     */
    private String logisticNumber;

    /**
     * 发货状态 0:未推送 1:已推送 3:已发货 4:已签收
     */
    private Integer logisticStatus;

    /**
     * 订单状态 10:初始状态 20:待发货 30:已发货 40:已签收  50:售后中 60：售后完成 90
     */
    private Integer orderStatus;

    /**
     * 订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 收货人
     */
    private String sendName;

    /**
     * 收货手机
     */
    private String sendPhone;

    /**
     * 收货地区
     */
    private String sendArea;

    /**
     * 收货详细地址
     */
    private String sendAddress;
    /**
     * 办理手机号
     */
    private String etcPhone;

    /**
     * 订单渠道
     */
    private String source;
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paidTime;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    /**
     * 收货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receivedTime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundAt;
    /**
     * 异常信息
     */
    private String msg;

    private String remark;

    private String cancelReason;

    private String paymentSn;

    private String transactionId;

    /**
     * 是否挂起，0-否，1-是
     */
    private Integer isPending;

    /**
     * 产品包名称
     */
    public String packageStr;
//    public String getPackageStr() {
//        return ProductPackageEnum.getDescByCode(packageSn);
//    }

    public String getStatusStr() {
        return ProductOrderStatusEnum.getDescByCode(orderStatus);
    }

    public Boolean isAllowRefund() {
        return ProductOrderStatusEnum.REFUNDING.getCode().equals(orderStatus);

    }

    public Boolean isAllowCancel() {
        return Arrays.asList(
                ProductOrderStatusEnum.PAID.getCode(), ProductOrderStatusEnum.DEFAULT.getCode(),
                ProductOrderStatusEnum.SHIPPED.getCode(), ProductOrderStatusEnum.RECEIVED.getCode()
        ).contains(orderStatus);
    }

    public Boolean isAllowModify() {
        return StringUtils.isEmpty(applyOrderSn);
    }

    public String referUserCode;

    public Boolean allowInvoice = false;
}
