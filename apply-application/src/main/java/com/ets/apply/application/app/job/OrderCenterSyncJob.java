package com.ets.apply.application.app.job;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.apply.application.app.business.OrderCenterSyncBusiness;
import com.ets.apply.application.common.bo.orderCenter.OrderCenterSyncJobBO;
import com.ets.apply.application.infra.entity.OrderOrderEntity;
import com.ets.apply.application.infra.service.OrderOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;



@Slf4j
@Component
public class OrderCenterSyncJob {
    @Autowired
    private OrderOrderService orderOrderService;
    @Autowired
    private OrderCenterSyncBusiness orderCenterSyncBusiness;
    /*
     *  定时同步订单中心数据
     */
    @XxlJob("orderCenterSyncHandler")
    public ReturnT<String> orderCenterSyncHandler(String params){
        OrderCenterSyncJobBO orderCenterSyncJobBO = JSONObject.parseObject(params, OrderCenterSyncJobBO.class);
        LocalDateTime startTime = null;
        LocalDateTime endTime   = null;
        LocalDateTime scheduleTime = null;
        //如果指定时间 String str = "2022-01-01T12:00:00";
        if(StringUtils.isNotEmpty(orderCenterSyncJobBO.getScheduleTime())){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
            scheduleTime = LocalDateTime.parse(orderCenterSyncJobBO.getScheduleTime(), formatter);
        }
        orderCenterSyncJobBO.setScheduleTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        switch (orderCenterSyncJobBO.getType()){
            case "current":
                // 获取当前日期
                LocalDate today = LocalDate.now();
                // 获取当前日期的开始时间
                startTime = LocalDateTime.now().minusMinutes(10);
                // 获取当前日期的结束时间
                endTime = today.atTime(23, 59, 59);
                break;
            case "pre":
                startTime = LocalDate.now().minusDays(1).atStartOfDay();
                endTime = LocalDate.now().minusDays(1).atTime(23, 59, 59);
                break;
            //指定日期
            case "scheduleDay":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                startTime = LocalDateTime.of(scheduleTime.toLocalDate(), LocalTime.MIN);
                endTime   = LocalDateTime.of(scheduleTime.toLocalDate(), LocalTime.MAX);
                break;
            //指定月份
            case "scheduleMonth":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                startTime = LocalDateTime.of(scheduleTime.toLocalDate().withDayOfMonth(1), LocalTime.MIN);
                endTime   = LocalDateTime.of(scheduleTime.toLocalDate().withDayOfMonth(scheduleTime.toLocalDate().lengthOfMonth()), LocalTime.MAX);
                break;
            //指定年份
            case "scheduleYear":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                LocalDateTime currentYearStartTime;
                for(int i = 1; i <= 12; i++){
                    currentYearStartTime = scheduleTime.withMonth(i).withDayOfMonth(1);
                    startTime = LocalDateTime.of(currentYearStartTime.toLocalDate().withDayOfMonth(1), LocalTime.MIN);
                    endTime   = LocalDateTime.of(currentYearStartTime.toLocalDate().withDayOfMonth(currentYearStartTime.toLocalDate().lengthOfMonth()), LocalTime.MAX);

                    List<OrderOrderEntity> orderList = orderOrderService.getOrderSnByUpdatedAt(startTime, endTime);
                    XxlJobLogger.log("订单数量："+orderList.size()+","+startTime.toString() + "---" +currentYearStartTime.toString());
                    orderList.forEach(order -> {
                        orderCenterSyncBusiness.orderCenterSync(order.getOrderSn(),true);
                    });
                }
                return ReturnT.SUCCESS;
        }

        List<OrderOrderEntity> orderList = orderOrderService.getOrderSnByUpdatedAt(startTime, endTime);
        //System.out.println("订单数量："+orderList.size()+","+startTime.toString() + "---" +endTime.toString());
        XxlJobLogger.log("订单数量："+orderList.size()+","+startTime.toString() + "---" +endTime.toString());

        orderList.forEach(order -> {
            orderCenterSyncBusiness.orderCenterSync(order.getOrderSn(),true);
        });
        return ReturnT.SUCCESS;
    }

    /*
     *  定时同步订单中心数据 按创建时间
     */
    @XxlJob("orderCenterSyncCreatedHandler")
    public ReturnT<String> orderCenterSyncCreatedHandler(String params){
        OrderCenterSyncJobBO orderCenterSyncJobBO = JSONObject.parseObject(params, OrderCenterSyncJobBO.class);
        LocalDateTime startTime = null;
        LocalDateTime endTime   = null;
        LocalDateTime scheduleTime = null;
        //如果指定时间 String str = "2022-01-01T12:00:00";
        if(StringUtils.isNotEmpty(orderCenterSyncJobBO.getScheduleTime())){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
            scheduleTime = LocalDateTime.parse(orderCenterSyncJobBO.getScheduleTime(), formatter);
        }
        orderCenterSyncJobBO.setScheduleTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        switch (orderCenterSyncJobBO.getType()){
            case "current":
                // 获取当前日期
                LocalDate today = LocalDate.now();
                // 获取当前日期的开始时间
                startTime = today.atStartOfDay();
                // 获取当前日期的结束时间
                endTime = today.atTime(23, 59, 59);
                break;
            case "pre":
                startTime = LocalDate.now().minusDays(1).atStartOfDay();
                endTime = LocalDate.now().minusDays(1).atTime(23, 59, 59);
                break;
            //指定日期
            case "scheduleDay":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                startTime = LocalDateTime.of(scheduleTime.toLocalDate(), LocalTime.MIN);
                endTime   = LocalDateTime.of(scheduleTime.toLocalDate(), LocalTime.MAX);
                break;
            //指定月份
            case "scheduleMonth":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                startTime = LocalDateTime.of(scheduleTime.toLocalDate().withDayOfMonth(1), LocalTime.MIN);
                endTime   = LocalDateTime.of(scheduleTime.toLocalDate().withDayOfMonth(scheduleTime.toLocalDate().lengthOfMonth()), LocalTime.MAX);
                break;
            //指定年份
            case "scheduleYear":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                LocalDateTime currentYearStartTime;
                for(int i = 1; i <= 12; i++){
                    currentYearStartTime = scheduleTime.withMonth(i).withDayOfMonth(1);
                    startTime = LocalDateTime.of(currentYearStartTime.toLocalDate().withDayOfMonth(1), LocalTime.MIN);
                    endTime   = LocalDateTime.of(currentYearStartTime.toLocalDate().withDayOfMonth(currentYearStartTime.toLocalDate().lengthOfMonth()), LocalTime.MAX);
                    sync(startTime, endTime);
                }
                return ReturnT.SUCCESS;
        }
        sync(startTime, endTime);
        return ReturnT.SUCCESS;
    }

    public void sync(LocalDateTime startTime, LocalDateTime endTime){
        IPage<OrderOrderEntity> orderList = orderOrderService.getOrderSnByCreatedAtAndPage(startTime, endTime, 1, 50000);
        if(orderList != null){
            log.info("start 订单数量："+orderList.getTotal()+"总页数"+orderList.getPages()+","+startTime.toString() + "---" +endTime.toString());
            orderList.getRecords().forEach(order -> {
                orderCenterSyncBusiness.orderCenterSync(order.getOrderSn(),true);
            });
            if(orderList.getPages() > 1){
                for (int i = 2; i <= orderList.getPages();i++){
                    IPage<OrderOrderEntity> orderList2 = orderOrderService.getOrderSnByCreatedAtAndPage(startTime, endTime, i, 50000);
                    orderList2.getRecords().forEach(order -> {
                        orderCenterSyncBusiness.orderCenterSync(order.getOrderSn(),true);
                    });
                }
            }
        }

    }


    /*
     *  定时同步订单中心数据
     */
    @XxlJob("orderCenterSyncByOrderSnHandler")
    public ReturnT<String> orderCenterSyncByOrderSnHandler(String orderSn){
        orderCenterSyncBusiness.orderCenterSync(orderSn,true);
        return ReturnT.SUCCESS;
    }

    /*
     *  初始化订单中心数据 once
     *  申办订单激活才更新为已完成，未激活为进行中
     */
    @XxlJob("orderCenterSyncOnceHandler")
    public ReturnT<String> orderCenterSyncOnceHandler(String params){
        OrderCenterSyncJobBO orderCenterSyncJobBO = JSONObject.parseObject(params, OrderCenterSyncJobBO.class);
        LocalDateTime startTime = null;
        LocalDateTime endTime   = null;
        LocalDateTime scheduleTime = null;
        //如果指定时间 String str = "2022-01-01T12:00:00";
        if(StringUtils.isNotEmpty(orderCenterSyncJobBO.getScheduleTime())){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
            scheduleTime = LocalDateTime.parse(orderCenterSyncJobBO.getScheduleTime(), formatter);
        }
        orderCenterSyncJobBO.setScheduleTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        switch (orderCenterSyncJobBO.getType()){
            case "current":
                // 获取当前日期
                LocalDate today = LocalDate.now();
                // 获取当前日期的开始时间
                startTime = today.atStartOfDay();
                // 获取当前日期的结束时间
                endTime = today.atTime(23, 59, 59);
                syncOnce(startTime, endTime);
                break;
            case "pre":
                startTime = LocalDate.now().minusDays(1).atStartOfDay();
                endTime = LocalDate.now().minusDays(1).atTime(23, 59, 59);
                syncOnce(startTime, endTime);
                break;
            //指定日期
            case "scheduleDay":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                startTime = LocalDateTime.of(scheduleTime.toLocalDate(), LocalTime.MIN);
                endTime   = LocalDateTime.of(scheduleTime.toLocalDate(), LocalTime.MAX);
                syncOnce(startTime, endTime);
                break;
            //指定月份
            case "scheduleMonth":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                startTime = LocalDateTime.of(scheduleTime.toLocalDate().withDayOfMonth(1), LocalTime.MIN);
                endTime   = LocalDateTime.of(scheduleTime.toLocalDate().withDayOfMonth(scheduleTime.toLocalDate().lengthOfMonth()), LocalTime.MAX);
                syncOnce(startTime, endTime);
                break;
            //指定年份
            case "scheduleYear":
                if(scheduleTime == null){
                    return new ReturnT<>(ReturnT.FAIL_CODE, "请指定日期");
                }
                LocalDateTime currentYearStartTime;
                for(int i = 1; i <= 12; i++){
                    currentYearStartTime = scheduleTime.withMonth(i).withDayOfMonth(1);
                    startTime = LocalDateTime.of(currentYearStartTime.toLocalDate().withDayOfMonth(1), LocalTime.MIN);
                    endTime   = LocalDateTime.of(currentYearStartTime.toLocalDate().withDayOfMonth(currentYearStartTime.toLocalDate().lengthOfMonth()), LocalTime.MAX);
                    syncOnce(startTime, endTime);
                }
                break;
        }
        return ReturnT.SUCCESS;
    }

    public void syncOnce(LocalDateTime startTime, LocalDateTime endTime){
        IPage<OrderOrderEntity> orderList = orderOrderService.getOrderSnByCreatedAtAndPageOnce(startTime, endTime, 1, 50000);
        if(orderList != null){
            log.info("start 订单数量："+orderList.getTotal()+"总页数"+orderList.getPages()+","+startTime.toString() + "---" +endTime.toString());
            orderList.getRecords().forEach(order -> {
                orderCenterSyncBusiness.orderCenterSync(order.getOrderSn(),false);
            });
            if(orderList.getPages() > 1){
                for (int i = 2; i <= orderList.getPages();i++){
                    IPage<OrderOrderEntity> orderList2 = orderOrderService.getOrderSnByCreatedAtAndPageOnce(startTime, endTime, i, 50000);
                    orderList2.getRecords().forEach(order -> {
                        orderCenterSyncBusiness.orderCenterSync(order.getOrderSn(),false);
                    });
                }
            }
        }

    }
}
