package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.ProductOrderLogEntity;
import com.ets.apply.application.infra.mapper.ProductOrderLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品订单日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
@Service
@DS("db-apply")
public class ProductOrderLogService extends BaseService<ProductOrderLogMapper, ProductOrderLogEntity> {

}
