package com.ets.apply.application.infra.service;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.common.bo.admin.ConfigBizFieldBO;
import com.ets.apply.application.common.bo.admin.ConfigBizFieldSubBO;
import com.ets.apply.application.infra.entity.ConfigBizFieldValuesEntity;
import com.ets.apply.application.infra.mapper.ConfigBizFieldValuesMapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 映射模块关联的产品包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
@DS("db-apply")
public class ConfigBizFieldValuesService extends BaseService<ConfigBizFieldValuesMapper, ConfigBizFieldValuesEntity> {

    /*
     * 通过module获取信息
     */
    public ConfigBizFieldValuesEntity getInfoByBizFieldAndKey(String bizField, String key){
        Wrapper<ConfigBizFieldValuesEntity> wrapper = Wrappers.<ConfigBizFieldValuesEntity>lambdaQuery()
                .eq(ConfigBizFieldValuesEntity::getBizField, bizField)
                .eq(ConfigBizFieldValuesEntity::getFieldKey, key)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .last("limit 1");
        return super.baseMapper.selectOne(wrapper);
    }

    /*
     * 通过biz_field获取信息，包含子集
     */
    public List<ConfigBizFieldBO> getListByBizFieldKey(String bizFieldKey,Boolean isIncludeKey){
        //获取biz_type的数据
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getId,ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
                .eq(ConfigBizFieldValuesEntity::getBizField, bizFieldKey)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        List<ConfigBizFieldValuesEntity> list = this.getListByWrapper(wrapper);
        List<ConfigBizFieldBO> boList = new ArrayList<>();
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                ConfigBizFieldBO bo = new ConfigBizFieldBO();
                bo.setValue(configBizFieldValues.getFieldKey());
                if(isIncludeKey){
                    bo.setLabel(configBizFieldValues.getName()+"【"+configBizFieldValues.getFieldKey()+"】");
                }else{
                    bo.setLabel(configBizFieldValues.getName());
                }
                //获取子集
                if(configBizFieldValues.getId() > 0){
                    bo.setSub(getSubListByParentId(configBizFieldValues.getId()));
                }
                boList.add(bo);
            }
        }
        return boList;
    }

    public List<ConfigBizFieldSubBO> getSubListByParentId(Integer parentId){
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
                .eq(ConfigBizFieldValuesEntity::getParentId, parentId)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        List<ConfigBizFieldValuesEntity> list = this.getListByWrapper(wrapper);
        List<ConfigBizFieldSubBO> boList = new ArrayList<>();
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                ConfigBizFieldSubBO bo = new ConfigBizFieldSubBO();
                bo.setValue(configBizFieldValues.getFieldKey());
                bo.setLabel(configBizFieldValues.getName()+"【"+configBizFieldValues.getFieldKey()+"】");
                boList.add(bo);
            }
        }
        return boList;
    }

    /*
     * 通过biz_field获取信息，包含子集
     */
    public HashMap<String, String> getListByBizFieldKeyMap(String bizFieldKey){
        //获取biz_type的数据
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getId,ConfigBizFieldValuesEntity::getFieldKey,ConfigBizFieldValuesEntity::getName)
                .eq(ConfigBizFieldValuesEntity::getBizField, bizFieldKey)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
                .orderByAsc(ConfigBizFieldValuesEntity::getSort);
        List<ConfigBizFieldValuesEntity> list = this.getListByWrapper(wrapper);
        List<ConfigBizFieldBO> boList = new ArrayList<>();
        HashMap<String,String> listMap = new HashMap<String,String>();
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                listMap.put(configBizFieldValues.getFieldKey(), configBizFieldValues.getName());
            }
        }
        return listMap;
    }

    /**
     *  判断任务类型是否为java
     */
    public boolean checkTaskIsJava(String fieldKey) {
        ConfigBizFieldValuesEntity configBizFieldValuesEntity = this.getInfoByBizFieldAndKey("task_refer_type", fieldKey);
        if(configBizFieldValuesEntity != null && Arrays.asList(1,3).contains(configBizFieldValuesEntity.getParentId())){
            return true;
        }
        return false;
    }

    /**
     *  获取默认不展示的refer_type
     */
    public List<String> getTaskReferTypeNotSearchList() {
        //获取biz_type的数据
        LambdaQueryWrapper<ConfigBizFieldValuesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ConfigBizFieldValuesEntity::getFieldKey)
               .eq(ConfigBizFieldValuesEntity::getBizField, "task_refer_type")
               .in(ConfigBizFieldValuesEntity::getParentId, 2,3)
               .eq(ConfigBizFieldValuesEntity::getStatus, 1);
        List<ConfigBizFieldValuesEntity> list =  this.getListByWrapper(wrapper);
        List<String> collect = new ArrayList<>();
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                collect.add(configBizFieldValues.getFieldKey());
            }
        }
        return collect;
    }

    public HashMap<Integer, String> getMapByBizFieldAndKey(String bizField, Set<Integer> keys){
        Wrapper<ConfigBizFieldValuesEntity> wrapper = Wrappers.<ConfigBizFieldValuesEntity>lambdaQuery()
                .eq(ConfigBizFieldValuesEntity::getBizField, bizField)
                .in(ConfigBizFieldValuesEntity::getFieldKey, keys)
                .eq(ConfigBizFieldValuesEntity::getStatus, 1)
              ;
        List<ConfigBizFieldValuesEntity> list = super.baseMapper.selectList(wrapper);
        HashMap<Integer, String> map = new HashMap<>();
        if(list != null){
            for (ConfigBizFieldValuesEntity configBizFieldValues : list) {
                map.put(Integer.valueOf(configBizFieldValues.getFieldKey()), configBizFieldValues.getName());
            }
        }
        return map;
    }
}
