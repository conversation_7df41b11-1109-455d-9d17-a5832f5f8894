package com.ets.apply.application.app.business.productPackageResource;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.app.business.BaseBusiness;
import com.ets.apply.application.common.consts.applyPage.ApplyPageStatusEnum;
import com.ets.apply.application.common.consts.applyPage.ApplyPageTypeEnum;
import com.ets.apply.application.common.consts.applyPageLog.ApplyPageLogTypeEnum;
import com.ets.apply.application.common.consts.cache.CacheKeyEnum;
import com.ets.apply.application.common.consts.productPackageResource.ResourceStatusEnum;
import com.ets.apply.application.common.dto.request.applyPage.ApplyPageCreateDTO;
import com.ets.apply.application.common.dto.request.applyPage.ApplyPageListDTO;
import com.ets.apply.application.common.dto.request.applyPage.ApplyPageModifyDTO;
import com.ets.apply.application.common.dto.request.applyPage.ApplyPageSetStatusDTO;
import com.ets.apply.application.common.dto.request.productPackageResource.*;
import com.ets.apply.application.common.vo.applyPage.ApplyPageVO;
import com.ets.apply.application.common.vo.productPackageResource.ProductPackageResourceListVO;
import com.ets.apply.application.common.vo.productPackageResource.ProductPackageResourceVO;
import com.ets.apply.application.infra.entity.ApplyPageEntity;
import com.ets.apply.application.infra.entity.ConfigBizFieldValuesEntity;
import com.ets.apply.application.infra.entity.ProductPackageResourceEntity;
import com.ets.apply.application.infra.service.ConfigBizFieldValuesService;
import com.ets.apply.application.infra.service.MapConfigService;
import com.ets.apply.application.infra.service.ProductPackageResourceService;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ProductPackageResourceBusiness extends BaseBusiness {
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private ProductPackageResourceService productPackageResourceService;

    @Resource(name = "defaultRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ConfigBizFieldValuesService configBizFieldValuesService;

    public void create(ProductPackageResourceCreateDTO createDTO) {
        ProductPackageResourceEntity productPackageResourceEntity = new ProductPackageResourceEntity();
        productPackageResourceEntity.setResourceSn(ToolsHelper.genNum(redisPermanentTemplate,
                "productPackageResourceSn", appConfig.getEnv(), 8));
        productPackageResourceEntity.setResourceName(createDTO.getResourceName());
        productPackageResourceEntity.setResourceType(createDTO.getResourceType());
        productPackageResourceEntity.setOperator(RequestHelper.getAdminOperator());
        productPackageResourceEntity.setContent(JSONObject.toJSONString(createDTO.getContent()));
        productPackageResourceEntity.setResourceStatus(ResourceStatusEnum.LISTING.getStatus());
        productPackageResourceService.create(productPackageResourceEntity);

    }

    public IPage<ProductPackageResourceListVO> getList(ResourceListDTO listDTO) {
        IPage<ProductPackageResourceEntity> oPage = new Page<>(listDTO.getPageNum(), listDTO.getPageSize(),
                true);

        // 查询条件设置
        LambdaQueryWrapper<ProductPackageResourceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(listDTO.getResourceSn()), ProductPackageResourceEntity::getResourceSn,
                        listDTO.getResourceSn())
                .like(StringUtils.isNotBlank(listDTO.getResourceName()), ProductPackageResourceEntity::getResourceName,
                        listDTO.getResourceName())
                .eq(ObjectUtil.isNotNull(listDTO.getResourceType()), ProductPackageResourceEntity::getResourceType,
                        listDTO.getResourceType())
                .eq(ObjectUtil.isNotNull(listDTO.getResourceStatus()), ProductPackageResourceEntity::getResourceStatus,
                        listDTO.getResourceStatus())
                .orderByDesc(ProductPackageResourceEntity::getCreatedAt)
                .orderByAsc(ProductPackageResourceEntity::getResourceStatus)
        ;
        IPage<ProductPackageResourceEntity> pageList = productPackageResourceService.getPageListByWrapper(oPage,
                wrapper);
        HashMap<Integer, String> resourceTypeMap = new HashMap<>();
        if (pageList.getTotal() > 0) {
            resourceTypeMap = configBizFieldValuesService.getMapByBizFieldAndKey("resource_type", pageList.getRecords().stream().map(ProductPackageResourceEntity::getResourceType).collect(Collectors.toSet()));
        }
        HashMap<Integer, String> finalResourceTypeMap = resourceTypeMap;
        return pageList.convert(record -> {
                    ProductPackageResourceListVO pageListVO = new ProductPackageResourceListVO();
                    BeanUtils.copyProperties(record, pageListVO);
                    pageListVO.setResourceTypeName(finalResourceTypeMap.get(record.getResourceType()));
                    return pageListVO;
                }
        );
    }


    public void modify(ProductPackageResourceModifyDTO modifyDTO) {

        ProductPackageResourceEntity resourceEntity =
                productPackageResourceService.getOneByColumn(modifyDTO.getResourceSn(),
                        ProductPackageResourceEntity::getResourceSn);
        if (ObjectUtil.isNull(resourceEntity)) {
            ToolsHelper.throwException("未找到需要修改的配置，请确认后重新提交");
        }
        LambdaUpdateWrapper<ProductPackageResourceEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageResourceEntity::getResourceSn, modifyDTO.getResourceSn())
                .set(ProductPackageResourceEntity::getContent, JSONObject.toJSONString(modifyDTO.getContent()))
                .set(ProductPackageResourceEntity::getResourceName, (modifyDTO.getResourceName()))
//                .set(ProductPackageResourceEntity::getRemark, modifyDTO.getRemark())
                .set(ProductPackageResourceEntity::getOperator, RequestHelper.getAdminOperator())

        ;
        productPackageResourceService.updateByWrapper(wrapper);
        // 修改时删除缓存
        redisTemplate.delete(CacheKeyEnum.PRODUCT_PACKAGE_RESOURCE_CACHE_KEY_PREFIX.getCode() + resourceEntity.getResourceSn());

    }

    public ProductPackageResourceVO getDetail(String resourceSn) {
        ProductPackageResourceEntity resourceEntity = productPackageResourceService.getOneByColumn(resourceSn,
                ProductPackageResourceEntity::getResourceSn);
        if (ObjectUtil.isNull(resourceEntity)) {
            ToolsHelper.throwException("未找到需要修改的资源配置，请确认后重新提交");
        }
        ProductPackageResourceVO packageResourceVO = new ProductPackageResourceVO();
        BeanUtils.copyProperties(resourceEntity, packageResourceVO);
        packageResourceVO.setContent(JSONObject.parseObject(resourceEntity.getContent()));
        ConfigBizFieldValuesEntity resourceTypeEntity = configBizFieldValuesService.getInfoByBizFieldAndKey(
                "resource_type",
                packageResourceVO.getResourceType().toString());
        if (ObjectUtil.isNotNull(resourceTypeEntity)) {
            packageResourceVO.setResourceTypeName(resourceTypeEntity.getName());
        }
        return packageResourceVO;
    }


    public void setStatus(ProductPackageResourceSetStatusDTO setStatusDTO) {
        ProductPackageResourceEntity resourceEntity =
                productPackageResourceService.getOneByColumn(setStatusDTO.getResourceSn(),
                        ProductPackageResourceEntity::getResourceSn);
        if (ObjectUtil.isNull(resourceEntity)) {
            ToolsHelper.throwException("未找到需要修改的资源配置，请确认后重新提交");
        }

        if (resourceEntity.getResourceStatus().equals(setStatusDTO.getStatus())) {
            return;
        }
        LambdaUpdateWrapper<ProductPackageResourceEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductPackageResourceEntity::getResourceSn, setStatusDTO.getResourceSn())
                .set(ProductPackageResourceEntity::getResourceStatus, setStatusDTO.getStatus())
                .set(ProductPackageResourceEntity::getOperator, RequestHelper.getAdminOperator())

        ;
        productPackageResourceService.updateByWrapper(wrapper);
        // 修改时删除缓存
        redisTemplate.delete(CacheKeyEnum.PRODUCT_PACKAGE_RESOURCE_CACHE_KEY_PREFIX.getCode() + setStatusDTO.getResourceSn());

    }

    public ProductPackageResourceVO getDetailWithCache(ProductPackageResourceDetailDTO detailDTO) {
        try {
            ProductPackageResourceVO resourceVO = new ProductPackageResourceVO();
            // 有缓存的话获取缓存的值
            String cacheResource =
                    redisTemplate.boundValueOps(CacheKeyEnum.PRODUCT_PACKAGE_RESOURCE_CACHE_KEY_PREFIX.getCode() + detailDTO.getResourceSn()).get();
            if (ObjectUtil.isNotNull(cacheResource)) {
                resourceVO = JSONObject.parseObject(cacheResource, ProductPackageResourceVO.class);
                if (ObjectUtil.isEmpty(resourceVO.getResourceSn())) {
                    return null;
                } else {
                    return resourceVO;
                }
            }
            ProductPackageResourceEntity resourceEntity = productPackageResourceService.getOneByColumn(detailDTO.getResourceSn(),
                    ProductPackageResourceEntity::getResourceSn);
            if (ObjectUtil.isNull(resourceEntity)) {
                ToolsHelper.throwException("未找到需要修改的资源配置，请确认后重新提交");
            }
            BeanUtils.copyProperties(resourceEntity, resourceVO);
            resourceVO.setContent(JSONObject.parseObject(resourceEntity.getContent()));
            // 设置缓存
            redisTemplate.boundValueOps(CacheKeyEnum.PRODUCT_PACKAGE_RESOURCE_CACHE_KEY_PREFIX.getCode() + detailDTO.getResourceSn()).set(JSONObject.toJSONString(resourceVO), 10, TimeUnit.MINUTES);

            return resourceVO;

        } catch (Exception e) {
            log.error("获取配置资源详情失败", e);
            return null;
        }

    }

    public HashMap<String, Object> getSelectOptions() {
        HashMap<String, Object> options = new HashMap<>();
        options.put("resourceType", configBizFieldValuesService.getListByBizFieldKey("resource_type", false));
        return options;
    }
}
