package com.ets.apply.application.app.thirdservice.fallback;

import com.alibaba.fastjson.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.CallPhpUserFeign;
import com.ets.apply.application.app.thirdservice.request.CreateWarrantyDTO;
import com.ets.apply.application.app.thirdservice.request.user.GetByThirdTypeThirdCodeDTO;
import com.ets.apply.application.app.thirdservice.response.user.GetByThirdTypeThirdCodeVO;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderAftersalesCancelUserCardDTO;
import com.ets.apply.application.common.dto.request.user.CheckQuotaDTO;
import com.ets.apply.application.common.dto.request.user.PlateNoCheckDuplicateDTO;
import com.ets.apply.application.common.dto.user.FindByUidDTO;
import com.ets.apply.application.common.dto.usercard.UserCardFindByOrderSnDTO;
import com.ets.apply.application.common.vo.usercard.UserCardFindByOrderSnVO;
import com.ets.common.JsonResult;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
public class CallPhpUserFallbackFactory implements FallbackFactory<CallPhpUserFeign> {

    @Override
    public CallPhpUserFeign create(Throwable cause) {
        return new CallPhpUserFeign() {
            @Override
            public String checkPlateDuplicate(@RequestBody PlateNoCheckDuplicateDTO dto) {
                return JsonResult.error("查询user 服务：is-plate-duplicate 请求第三方服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String checkQuota(CheckQuotaDTO dto) {
                return JsonResult.error("查询user 服务：check-quota 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public JsonResult<JSONObject> findByUid(FindByUidDTO dto) {
                return JsonResult.error("查询user 服务：findByUid 请求第三方服务失败: " + cause.getMessage());
            }
            @Override
            public String cancelUserCard(OrderAftersalesCancelUserCardDTO dto) {
                return JsonResult.error("操作user 服务：cancelUserCard 请求第三方服务失败: " + cause.getMessage()).toString();
            }
            @Override
            public String updateUserApplyOrderSn(OrderAftersalesCancelUserCardDTO dto) {
                return JsonResult.error("操作user 服务：updateUserApplyOrderSn 请求第三方服务失败: " + cause.getMessage()).toString();
            }

            @Override
            public String createWarranty(CreateWarrantyDTO dto) {
                return JsonResult.error("操作user 接口：createWarranty 新增质保记录失败：" + cause.getMessage()).toString();
            }

            @Override
            public JsonResult<JSONObject> findUserRealName(FindByUidDTO dto) {
                return JsonResult.error("查询user 服务：findUserRealName 请求第三方服务失败: " + cause.getMessage());
            }
            @Override
            public JsonResult<JSONObject> deleteUserRealName(FindByUidDTO dto) {
                return JsonResult.error("查询user 服务：deleteUserRealName 请求第三方服务失败: " + cause.getMessage());
            }

            @Override
            public JsonResult<UserCardFindByOrderSnVO> findByOrderSn(UserCardFindByOrderSnDTO dto) {
                return JsonResult.error("查询user 服务：findByOrderSn 请求第三方服务失败: " + cause.getMessage());
            }
            @Override
            public JsonResult<GetByThirdTypeThirdCodeVO> getByThirdTypeThirdCode(GetByThirdTypeThirdCodeDTO dto) {
                return JsonResult.error("查询user 服务：getByThirdTypeThirdCode 请求第三方服务失败: " + cause.getMessage());
            }
        };
    }
}
