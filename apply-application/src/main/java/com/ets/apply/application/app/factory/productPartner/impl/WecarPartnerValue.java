package com.ets.apply.application.app.factory.productPartner.impl;
import com.ets.apply.application.app.service.thirdPartner.WecarService;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderShipBO;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.consts.wecar.WecarStatusEnum;
import com.ets.apply.application.common.consts.wecar.WecarSwitchLogisticsStatusEnum;
import com.ets.apply.application.common.dto.request.wecar.WecarNotifyStatusDTO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.service.ProductOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class WecarPartnerValue extends ProductPartnerBase {

    @Autowired
    private WecarService wecarService;
    @Autowired
    private ProductOrderService productOrderService;

    /**
     * 调用wecar 通知取消退款
     */
    public String refund(ProductOrderRefundBO productOrderRefundBO) {
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(productOrderRefundBO.getProductOrderSn(), ProductOrderEntity::getProductOrderSn);
        WecarNotifyStatusDTO wecarNotifyStatusDTO = new WecarNotifyStatusDTO();
        wecarNotifyStatusDTO.setStatus(WecarStatusEnum.TO_REFUND.getStatus());
        wecarNotifyStatusDTO.setSpOrderStatus(WecarStatusEnum.getSpOrderStatus(WecarStatusEnum.TO_REFUND.getStatus()));
        wecarNotifyStatusDTO.setLogisticsStatus(WecarSwitchLogisticsStatusEnum.getChangeOutByCode(productOrderEntity.getLogisticStatus()));
        wecarNotifyStatusDTO.setActionTime(System.currentTimeMillis()/1000);
        wecarNotifyStatusDTO.setUserCode(productOrderEntity.getReferUserCode());
        wecarNotifyStatusDTO.setVehicleNo(productOrderEntity.getPlateNo());
        wecarNotifyStatusDTO.setOutOrderId(productOrderEntity.getProductOrderSn());
        wecarService.notifyStatus(wecarNotifyStatusDTO);
        return "";
    }


    /**
     * 通知wecar 发货
     *
     * @param productOrderShipBO
     */
    public void ship(ProductOrderShipBO productOrderShipBO) {
        ProductOrderEntity productOrderEntity = productOrderService.getOneByColumn(productOrderShipBO.getProductOrderSn(), ProductOrderEntity::getProductOrderSn);
        //已关闭或者退款中，不需要通知
        if(Arrays.asList(
            ProductOrderStatusEnum.CLOSED.getCode(),
            ProductOrderStatusEnum.REFUNDING.getCode()
        ).contains(productOrderEntity.getOrderStatus())){
            return;
        }

        WecarNotifyStatusDTO wecarNotifyStatusDTO = new WecarNotifyStatusDTO();
        wecarNotifyStatusDTO.setStatus(WecarStatusEnum.SHIPPED.getStatus());
        wecarNotifyStatusDTO.setOutOrderId(productOrderEntity.getProductOrderSn());
        wecarNotifyStatusDTO.setSpOrderStatus(WecarStatusEnum.getSpOrderStatus(WecarStatusEnum.SHIPPED.getStatus()));
        wecarNotifyStatusDTO.setLogisticsStatus(WecarSwitchLogisticsStatusEnum.getChangeOutByCode(productOrderEntity.getLogisticStatus()));
        wecarNotifyStatusDTO.setActionTime(System.currentTimeMillis()/1000);
        wecarNotifyStatusDTO.setUserCode(productOrderEntity.getReferUserCode());
        wecarNotifyStatusDTO.setVehicleNo(productOrderEntity.getPlateNo());
        wecarService.notifyStatus(wecarNotifyStatusDTO);
    }
}
