package com.ets.apply.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ets.apply.application.app.thirdservice.feign.UserApplicationFeign;
import com.ets.apply.application.common.consts.AiAgentValidateResultEnum;
import com.ets.apply.application.common.consts.IssuerProvinceNameEnum;
import com.ets.apply.application.common.consts.userCard.UserCardStatusEnum;
import com.ets.apply.application.common.consts.validatePlateNo.ValidatePlateNoEnum;
import com.ets.apply.application.common.dto.aiagent.AiAgentOrderInfoDTO;
import com.ets.apply.application.common.dto.aiagent.AiAgentPlateNoValidateDTO;
import com.ets.apply.application.common.vo.aiagent.AiAgentOrderInfoVO;
import com.ets.apply.application.common.vo.aiagent.AiAgentPlateNoValidateVO;
import com.ets.apply.application.common.vo.user.UsersCardsEntityVO;
import com.ets.apply.application.infra.entity.*;
import com.ets.apply.application.infra.service.*;
import com.ets.common.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class AiAgentBusiness {

    @Autowired
    private OrderBusiness orderBusiness;

    @Autowired
    private OrderOrderService orderOrderService;

    @Autowired
    private CardsService cardsService;

    @Autowired
    private CardsIssuersService cardsIssuersService;

    @Autowired
    private ProductPackageService productPackageService;

    @Autowired
    private ValidatePlateNoService validatePlateNoService;

    @Autowired
    private UserApplicationFeign userApplicationFeign;

    public AiAgentOrderInfoVO getOrderInfo(AiAgentOrderInfoDTO aiAgentOrderInfoDTO) {

        AiAgentOrderInfoVO aiAgentOrderInfoVO = new AiAgentOrderInfoVO();

        OrderOrderEntity order = orderOrderService.getOrdersByPlateNoAndUid(aiAgentOrderInfoDTO.getPlateNo(), aiAgentOrderInfoDTO.getUid());
        if (ObjectUtils.isNotEmpty(order)) {
            BeanUtil.copyProperties(order, aiAgentOrderInfoVO);

            // 产品包
            if (ObjectUtils.isNotEmpty(order.getPackageSn())) {
                ProductPackageEntity productPackage = productPackageService.getBySn(order.getPackageSn());
                if (ObjectUtils.isNotEmpty(productPackage)) {
                    if (ObjectUtils.isNotEmpty(productPackage.getPackageInfo())) {
                        JSONObject jsonObject = JSONObject.parseObject(productPackage.getPackageInfo());
                        if (ObjectUtils.isNotEmpty(jsonObject)) {
                            Integer manufacturer = jsonObject.getInteger("manufacturer");
                            aiAgentOrderInfoVO.setManufacturer(manufacturer);

                            String deviceName = jsonObject.getString("device_name");
                            aiAgentOrderInfoVO.setDeviceName(deviceName);
                        }
                    }
                }
            }

            // 订单卡
            CardsEntity cards = cardsService.getByCardId(order.getCardId());

            if (ObjectUtils.isNotEmpty(cards)) {
                // 签约方式
                Integer paymentMode = orderBusiness.getPaymenMode(order, cards);
                aiAgentOrderInfoVO.setPaymentMode(paymentMode);

                // 订单发卡方
                CardsIssuersEntity cardsIssuers = cardsIssuersService.getByIssuerId(cards.getIssuerId());
                if (ObjectUtils.isNotEmpty(cardsIssuers)) {
                    AiAgentOrderInfoVO.Card.Issuer issuer = new AiAgentOrderInfoVO.Card.Issuer();
                    BeanUtil.copyProperties(cardsIssuers, issuer);

                    AiAgentOrderInfoVO.Card card = new AiAgentOrderInfoVO.Card();
                    card.setId(cards.getId());
                    card.setIssuer(issuer);

                    aiAgentOrderInfoVO.setCard(card);
                }
            }

        }

        return aiAgentOrderInfoVO;
    }

    public AiAgentPlateNoValidateVO getPlateNoValidate(AiAgentPlateNoValidateDTO validateDTO) {
        AiAgentPlateNoValidateVO validateVO = new AiAgentPlateNoValidateVO();

        // 查询车牌占用
        ValidatePlateNoEntity validatePlateNo = validatePlateNoService.getLastValidatePlateNo(validateDTO.getPlateNo(), validateDTO.getPlateColor());
        if (ObjectUtils.isNotEmpty(validatePlateNo)) {
            // 车牌占用
            if (validatePlateNo.getValidateStatus().equals(ValidatePlateNoEnum.VALIDATE_STATUS_FAIL.getCode())) {
                // 特殊处理北京车牌占用无省份 读取最新一条江苏车牌校验记录用于展示
                if (validatePlateNo.getIssuerCode().equals("BSuTong")) {
                    ValidatePlateNoEntity jsValidate = validatePlateNoService.getLastValidatePlateNo(validateDTO.getPlateNo(), validateDTO.getPlateColor(), "JSuTong");
                    if (ObjectUtils.isNotEmpty(jsValidate) && jsValidate.getValidateStatus().equals(ValidatePlateNoEnum.VALIDATE_STATUS_FAIL.getCode())) {
                        validatePlateNo = jsValidate;
                    }
                }
                // 查询是否我司用户
                boolean ourCompany = isOurCompany(validateDTO);

                if (ourCompany) {
                    validateVO.setValidateResult(AiAgentValidateResultEnum.OUR_COMPANY.getValue());
                } else {
                    validateVO.setValidateResult(AiAgentValidateResultEnum.NOT_OUR_COMPANY.getValue());
                }

                // 识别车牌占用省份
                String province = getIssuerProvince(validatePlateNo.getValidateResult());
                validateVO.setProvince(province);
            } else {
                // 无车牌占用
                validateVO.setValidateResult(AiAgentValidateResultEnum.NO_USE.getValue());
            }

        }

        return validateVO;
    }

    private boolean isOurCompany(AiAgentPlateNoValidateDTO validateDTO) {
        boolean ourCompany = false;
        try {
            JsonResult<UsersCardsEntityVO> jsonResult = userApplicationFeign.getByUidAndPlateNo(
                    validateDTO.getUid(),
                    validateDTO.getPlateNo(),
                    validateDTO.getPlateColor());
            UsersCardsEntityVO usersCards = jsonResult.getDataWithCheckError();
            if (ObjectUtils.isNotEmpty(usersCards) && usersCards.getStatus() == UserCardStatusEnum.Normal.getCode()) {
                ourCompany = true;
            }
        } catch (Exception e) {
            log.warn("查询是否我司用户异常: {}", e.getMessage());
        }
        return ourCompany;
    }

    private static String getIssuerProvince(String msg) {
        String province = "";
        int lastPos = -1;

        for (String pro : IssuerProvinceNameEnum.list) {
            int pos = msg.lastIndexOf(pro);
            if (pos != -1 && pos > lastPos) {
                lastPos = pos;
                province = pro;
            }
        }

        return province;
    }
}
