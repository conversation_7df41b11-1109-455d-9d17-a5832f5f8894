package com.ets.apply.application.common.dto.usercard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
public class UserCardFindByOrderSnDTO {

    @JsonProperty("order_sn")
    private String orderSn;

    private Long uid;

    private Fields fields = new Fields();

    @Data
    public static class Fields {
        @JsonProperty("0")
        private String cardNo = "card_no";

        @JsonProperty("1")
        private String cardId = "card_id";

        @JsonProperty("2")
        private String firstActivatedAt = "first_activated_at";

        @JsonProperty("vehicle_obu")
        private List<String> vehicleObu = Arrays.asList(
                "warranty_start_at", "warranty_end_at"
        );
    }
}
