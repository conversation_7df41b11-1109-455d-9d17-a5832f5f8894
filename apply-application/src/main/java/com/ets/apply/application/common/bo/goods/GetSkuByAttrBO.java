package com.ets.apply.application.common.bo.goods;

import lombok.Data;

@Data
public class GetSkuByAttrBO {
    private String stallCode;
    private String etcType = "card_obu";
    private Boolean coverLack = false;
    private Integer issuerId;
    private Integer manufacturer;
    private Integer deviceType;
    private Integer deviceColor;
    private Integer deviceVersion;

    public GetSkuByAttrBO(String stallCode, Integer issuerId, Integer manufacturer, Integer deviceType, Integer deviceColor, Integer deviceVersion) {
        this.stallCode = stallCode;
        this.issuerId = issuerId;
        this.manufacturer = manufacturer;
        this.deviceType = deviceType;
        this.deviceColor = deviceColor;
        this.deviceVersion = deviceVersion;
    }
}
