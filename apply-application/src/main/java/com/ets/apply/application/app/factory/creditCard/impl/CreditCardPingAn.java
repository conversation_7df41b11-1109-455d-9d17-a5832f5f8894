package com.ets.apply.application.app.factory.creditCard.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ets.apply.application.app.business.creditCard.CreditCardRewardBusiness;
import com.ets.apply.application.app.service.bank.PabService;
import com.ets.apply.application.common.bo.creditCard.CreditCardRewardBO;
import com.ets.apply.application.common.bo.creditCard.CreditCardRewardChannelBO;
import com.ets.apply.application.common.config.creditBank.PabCreditBankConfig;
import com.ets.apply.application.common.consts.activityCreditCardUsersInfo.ActivityCreditCardUserInfoClassifyEnum;
import com.ets.apply.application.common.consts.service.ServerErrorCodeEnum;
import com.ets.apply.application.common.dto.request.bank.pab.PabApplyOrderDto;
import com.ets.apply.application.common.dto.request.creditCard.ApplyOrderDTO;
import com.ets.apply.application.common.dto.request.creditCard.CheckByPhoneDTO;
import com.ets.apply.application.common.vo.creditCard.CreditCardApplyOrderVO;
import com.ets.apply.application.common.vo.creditCard.pab.CheckNewUserByPhoneVO;
import com.ets.apply.application.common.vo.creditCard.pab.PabResponseVO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import com.ets.apply.application.infra.entity.ActivityCreditCardUsersInfoEntity;
import com.ets.apply.application.infra.service.ActivityCreditCardBankUsersService;
import com.ets.apply.application.infra.service.ActivityCreditCardUsersInfoService;
import com.ets.common.BizException;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Random;

@Component
@Slf4j
public class CreditCardPingAn extends CreditCardBase {

    @Autowired
    private ActivityCreditCardUsersInfoService activityCreditCardUsersInfoService;
    @Autowired
    private ActivityCreditCardBankUsersService activityCreditCardBankUsersService;

    @Autowired
    private CreditCardRewardBusiness creditCardRewardBusiness;

    @Autowired
    private PabService pabService;

    @Autowired
    private PabCreditBankConfig pabCreditBankConfig;


    /**
     * 平安：当新户首刷成功后，记录该用户拥有领券资格；
     *
     * @param creditCardRewardBO
     */
    public void batchReward(CreditCardRewardBO creditCardRewardBO) {
        List<ActivityCreditCardBankUsersEntity> rewardListBank = activityCreditCardBankUsersService.getPingAnWaitingRewardList(
                creditCardRewardBO.getStartTime(),
                creditCardRewardBO.getEndTime(),
                creditCardRewardBO.getClassify()
        );
        if (!CollectionUtils.isEmpty(rewardListBank)) {
            log.info("batchReward：平安银行待发券总条数：" + rewardListBank.size());
            rewardListBank.forEach(bankInfo -> {
                try {
                    ActivityCreditCardUsersInfoEntity userInfo = activityCreditCardUsersInfoService.getOneByColumn(bankInfo.getApplyNumber(), ActivityCreditCardUsersInfoEntity::getOrderSn);
                    if (ObjectUtil.isNull(userInfo)) {
                        ToolsHelper.throwException("银行用户数据缺失，单号" + bankInfo.getApplyNumber());
                    }
                    CreditCardRewardChannelBO channelBO = new CreditCardRewardChannelBO();
                    channelBO.setClassify(userInfo.getClassify());
                    channelBO.setWhichBank(userInfo.getWhichBank());
                    channelBO.setUid(userInfo.getUid());
                    channelBO.setOrderSn(userInfo.getOrderSn());
                    channelBO.setUserInfoId(userInfo.getId());
                    channelBO.setBankUserId(bankInfo.getId());
                    creditCardRewardBusiness.rewardChannel(channelBO);
                } catch (Exception e) {
                    log.info("发送消费券资格失败，流水单号：" + bankInfo.getApplyNumber() + e.getMessage());
                }
            });
        }
    }

    /**
     * 申请信用卡
     * @param applyOrderDTO
     * @return
     */
    public CreditCardApplyOrderVO applyOrder(ApplyOrderDTO applyOrderDTO) {
        //内部版本号
        applyOrderDTO.setInnerVersion(pabCreditBankConfig.getInnerVersion());

        CreditCardApplyOrderVO applyOrderVO = new CreditCardApplyOrderVO();
        //构造申请参数
        String partnerSeqId = applyOrderDTO.getBankApplyNumber();
        if(partnerSeqId.isEmpty()){
            String pattern = "yyyyMMddHHmmssSSS";
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            String formattedDateTime = sdf.format(calendar.getTime());
            Random random = new Random();
            partnerSeqId = pabCreditBankConfig.getProNum()+formattedDateTime+Integer.toString(random.nextInt(9000) + 1000);
        }

        String redirectUrl = applyOrderDTO.getRedirectUrl();
        if(redirectUrl.isEmpty()){
            if(applyOrderDTO.getClassify().equals(ActivityCreditCardUserInfoClassifyEnum.TYPE_PRODUCT_ORDER_APPLY.getCode())){
                redirectUrl = pabCreditBankConfig.getCallBackUrlH5();
            }else{
                redirectUrl = pabCreditBankConfig.getCallBackUrlMina();
            }
        }
        PabApplyOrderDto pabApplyOrderDto = new PabApplyOrderDto();
        pabApplyOrderDto.setMt(pabCreditBankConfig.getMt());
        pabApplyOrderDto.setPartnerSeqId(partnerSeqId);
        pabApplyOrderDto.setRedirectUrl(redirectUrl);
        ///log.info("CreditCardPingAn pabApplyUrlReqVO：" + pabApplyOrderDto.toString());
        PabResponseVO pabResponseVO =  pabService.getApplyUrl(pabApplyOrderDto);
        //log.info("CreditCardPingAn applyOrder：" + pabResponseVO.toString());
        //结果处理
        switch (pabResponseVO.getResponseCode()){
            case "000000":
                //新增用户申请信用卡记录和银行初始数据记录
                if(applyOrderDTO.getBankApplyNumber().isEmpty()){
                    this.saveBankOrder(applyOrderDTO, partnerSeqId);
                }
                applyOrderVO.setUrl(pabResponseVO.getData());
                break;
            case "900703":
                if(pabResponseVO.getResponseMsg().contains("TO_ERROR_PAGE_PARTNERSEQID_HAS_USED_IN_WXHZF_ERROR")){
                    ToolsHelper.throwException("您的申请已提交，请等待审核结果", ServerErrorCodeEnum.SERVICE_ERROR_CODE_CREDIT_CARD_SUBMITTED.getCode());
                }
                ToolsHelper.throwException("服务器开小差了，请稍后再试!");
                break;
            default:
                ToolsHelper.throwException("服务器开小差了，请稍后再试!");
                break;
        }
        return applyOrderVO;

    }

    /*
     * 通过手机号码判断新用户
     */
    public CheckNewUserByPhoneVO checkNewUserByPhone(CheckByPhoneDTO dto) {
        try {
            CheckNewUserByPhoneVO checkNewUserByPhoneVO = new CheckNewUserByPhoneVO();
            checkNewUserByPhoneVO.setIsNewUser(pabService.checkIsNewByPhone(dto.getUserPhone()));
            return checkNewUserByPhoneVO;
        } catch (Exception e) {
            log.info("平安银行判断新用户异常：" + e.getMessage());
            ToolsHelper.throwException("平安银行判断新用户异常，请稍后再试");
        }
        return null;
    }

}
