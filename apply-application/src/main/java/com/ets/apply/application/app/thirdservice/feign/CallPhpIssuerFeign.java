package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.CallPhpIssuerFallbackFactory;
import com.ets.apply.application.app.thirdservice.request.issuer.IssuerGetInvoiceUrlByOrderDTO;
import com.ets.apply.application.common.dto.request.issuer.*;
import com.ets.apply.application.common.dto.request.issuerAftersales.*;
import com.ets.apply.application.common.dto.request.orderAftersales.OrderCancelCheckDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(
        url = "${params.microUrls.etc-micro-issuer}",
        name = "CallPhpIssuerFeign",
        fallbackFactory = CallPhpIssuerFallbackFactory.class
)
public interface CallPhpIssuerFeign {

    @PostMapping(value = "/issuer/j-su-tong/check-card-state", headers = {"issuer-code=JSuTong"})
    String checkCardState(@RequestBody IssuerCheckCardStateDTO dto);

    @PostMapping(value = "/issuer/j-su-tong/recharge-order-check", headers = {"issuer-code=JSuTong"})
    String rechargeOrderCheck(@RequestBody IssuerRechargeOrderCheckDTO dto);

    @PostMapping(value = "/issuer/j-su-tong/write-card-init", headers = {"issuer-code=JSuTong"})
    String writeCardInit(@RequestBody IssuerWriteCardInitDTO dto);

    @PostMapping(value = "/issuer/j-su-tong/write-card-apply", headers = {"issuer-code=JSuTong"})
    String writeCardApply(@RequestBody IssuerWriteCardApplyDTO dto);

    @PostMapping(value = "/issuer/j-su-tong/write-card-notify", headers = {"issuer-code=JSuTong"})
    String writeCardNotify(@RequestBody IssuerWriteCardNotifyDTO dto);

    @PostMapping(value = "/issuer/j-su-tong/write-card-exception-handling", headers = {"issuer-code=JSuTong"})
    String writeCardExceptionHandling(@RequestBody IssuerWriteCardExceptionDTO dto);

    @PostMapping(value = "/issuer/common/check-plate-no")
    String checkPlateNoUnique(@RequestHeader(name = "ISSUER-CODE") String issuerCode, @RequestBody IssuerCheckPlateNoUniqueDTO plateNoUniqueDto);

    /*
     * 取消申办前校验
     * https://yapi.etczs.net/project/152/interface/api/28776
     */
    @PostMapping(value = "/issuer/order/cancel-check")
    String orderCancelCheck(@RequestHeader(name = "ISSUER-CODE") String issuerCode, @RequestBody OrderCancelCheckDTO dto);


    /*
     * 取消申办
     * http://yapi.etczs.net/project/152/interface/api/4375
     */
    @PostMapping(value = "/issuer/order/cancel")
    String orderCancel(@RequestHeader(name = "ISSUER-CODE") String issuerCode, @RequestBody IssuerOrderCancelDTO dto);

    /*
     * 重置申办发行
     * http://yapi.etczs.net/project/152/interface/api/28767
     */
    @PostMapping(value = "/issuer/order/clear-issue")
    String orderReset(@RequestHeader(name = "ISSUER-CODE") String issuerCode, @RequestBody IssuerOrderResetDTO dto);

    @PostMapping("/issuer/invoice/get-invoice-url-by-order")
    String getInvoiceUrlByOrder(@RequestHeader(name = "ISSUER-CODE") String issuerCode, @RequestBody IssuerGetInvoiceUrlByOrderDTO dto);
}
