package com.ets.apply.application.infra.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.apply.application.infra.entity.OcrResultRecordEntity;
import com.ets.apply.application.infra.mapper.OcrResultRecordMapper;
import org.springframework.stereotype.Service;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@Service
@DS("db-service-proxy")
public class OcrResultRecordService extends BaseService<OcrResultRecordMapper, OcrResultRecordEntity> {
    public JSONObject getBySn(String recordSn) {
        LambdaQueryWrapper<OcrResultRecordEntity> wrapper = Wrappers.<OcrResultRecordEntity>lambdaQuery()
                .eq(OcrResultRecordEntity::getRecordSn, recordSn);
        OcrResultRecordEntity record = super.baseMapper.selectOne(wrapper);
        if(record != null){
            try {
                return  JSONObject.parseObject(record.getResult());
            }catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}
