package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.apply.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.apply.application.common.dto.request.TaskRecordQueryDto;
import com.ets.apply.application.common.dto.task.TaskRecordDTO;
import com.ets.apply.application.infra.entity.TaskRecordEntity;
import com.ets.apply.application.infra.mapper.TaskRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * task任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Slf4j
@Service
@DS("db-apply")
public class TaskRecordService extends BaseService<TaskRecordMapper, TaskRecordEntity>{

    public IPage<TaskRecordEntity> getPageListByWrapper(IPage<TaskRecordEntity> page, Wrapper<TaskRecordEntity> wrapper) {

        return super.baseMapper.selectPage(page, wrapper);
    }

    public IPage<TaskRecordEntity> getPageListByDto(TaskRecordQueryDto taskRecordQueryDto) {
        // 分页设置
        IPage<TaskRecordEntity> oPage = new Page<>(taskRecordQueryDto.getPageNum(), taskRecordQueryDto.getPageSize(), true);
        // 查询条件设置
        LambdaQueryWrapper<TaskRecordEntity> wrapper = new QueryWrapper<TaskRecordEntity>().lambda();

        wrapper.eq(! StringUtils.isEmpty(taskRecordQueryDto.getTaskSn()),TaskRecordEntity::getTaskSn, taskRecordQueryDto.getTaskSn());


        return super.baseMapper.selectPage(oPage, wrapper);
    }

    /*
     * 创建task
     */
    public TaskRecordEntity addNew(TaskRecordDTO taskRecordDTO) {
        TaskRecordEntity taskRecord = new TaskRecordEntity();
        taskRecord.setTaskSn(taskRecordDTO.getTaskSn());
        taskRecord.setReferSn(taskRecordDTO.getReferSn());
        taskRecord.setReferType(taskRecordDTO.getReferType());
        taskRecord.setNotifyContent(taskRecordDTO.getNotifyContent());
        taskRecord.setNextExecTime(taskRecordDTO.getNextExecTime());
        taskRecord.setNotifyContent(taskRecordDTO.getNotifyContent());
        taskRecord.setStatus(taskRecordDTO.getStatus());
        taskRecord.setCreatedAt(LocalDateTime.now());
        taskRecord.setUpdatedAt(LocalDateTime.now());
        this.save(taskRecord);
        return taskRecord;
    }

    /*
     * 通过taskSn获取
     */
    public TaskRecordEntity getOneByTaskSn(String taskSn) {
        Wrapper<TaskRecordEntity> wrapper = Wrappers.<TaskRecordEntity>lambdaQuery()
                .eq(TaskRecordEntity::getTaskSn, taskSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /*
     * 获取创建时间之前的数据
     */
    public List<TaskRecordEntity> getListByCreatedAt(Integer minusDays) {
        LambdaQueryWrapper<TaskRecordEntity> wrapper = new QueryWrapper<TaskRecordEntity>().lambda()
                .in(TaskRecordEntity::getStatus, Arrays.asList(
                        TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode(),
                        TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode()
                ))
                .in(TaskRecordEntity::getReferType, TaskRecordReferTypeEnum.getTypeList())
                .gt(TaskRecordEntity::getCreatedAt, LocalDateTime.now().minusDays(minusDays))
                .orderByAsc(TaskRecordEntity::getCreatedAt)
                .last("limit 10000");
        return super.baseMapper.selectList(wrapper);
    }
    /*
     * 根据类型获取创建时间之前的数据
     */
    public List<TaskRecordEntity> getListByCreatedAtAndReferType(String referType, Integer minusDays) {
        LambdaQueryWrapper<TaskRecordEntity> wrapper = new QueryWrapper<TaskRecordEntity>().lambda()
                .in(TaskRecordEntity::getStatus, Arrays.asList(
                        TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode(),
                        TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode()
                ))
                .eq(TaskRecordEntity::getReferType, referType)
                .gt(TaskRecordEntity::getCreatedAt, LocalDateTime.now().minusDays(minusDays))
                .orderByAsc(TaskRecordEntity::getCreatedAt)
                .last("limit 10000");
        return super.baseMapper.selectList(wrapper);
    }

    /*
     * 通过referSn获取
     */
    public TaskRecordEntity getOneByReferSn(String referSn) {
        Wrapper<TaskRecordEntity> wrapper = Wrappers.<TaskRecordEntity>lambdaQuery()
                .eq(TaskRecordEntity::getReferSn, referSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
