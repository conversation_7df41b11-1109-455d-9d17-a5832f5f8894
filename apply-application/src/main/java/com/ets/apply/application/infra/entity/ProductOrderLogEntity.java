package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 商品订单日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("product_order_log")
public class ProductOrderLogEntity extends BaseEntity<ProductOrderLogEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品订单号
     */
    private String productOrderSn;

    /**
     * 1:创建,2:取消,3:退款,4:备注,5:发货,6:发起售后,7:取消售后,8:完成售后,9:快递签收
     */
    private Integer logType;

    private Integer preStatus;

    private Integer afterStatus;

    private Integer logisticStatus;

    private String operateUser;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;


}
