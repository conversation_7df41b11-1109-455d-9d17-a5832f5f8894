package com.ets.apply.application.infra.service;

import java.util.Date;

import com.ets.apply.application.common.dto.taxi.ImportListDto;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.dynamic.datasource.annotation.DS;

import com.ets.apply.application.infra.entity.TaxiImportEntity;
import com.ets.apply.application.infra.entity.TaxiCompanyEntity;
import com.ets.apply.application.infra.mapper.TaxiImportMapper;
import com.ets.apply.application.common.bo.taxi.TaxiImportItemBo;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Slf4j
@Service
@DS("db-etc")
public class TaxiImportService extends BaseService<TaxiImportMapper, TaxiImportEntity> {
    public TaxiImportEntity findOrCreate(TaxiImportItemBo bo, TaxiCompanyEntity companyEntity) {
        LambdaQueryWrapper<TaxiImportEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaxiImportEntity::getNumber, bo.getNumber())
                .eq(TaxiImportEntity::getPlateNo, bo.getPlateNo());
        TaxiImportEntity entity = this.baseMapper.selectOne(queryWrapper);
        if (entity != null) {
            return entity;
        }

        entity = new TaxiImportEntity();
        entity.setCompanyId(companyEntity.getId());
        entity.setSendName(bo.getSendName());
        entity.setSendPhone(bo.getSendPhone());
        entity.setSendArea(bo.getSendArea());
        entity.setSendAddress(bo.getSendAddress());
        entity.setPlateNo(bo.getPlateNo());
        entity.setName(bo.getName());
        entity.setNumber(bo.getNumber());
        entity.setValidDate(bo.getValidDate());
        entity.setRegisterNo(bo.getRegisterNo());
        entity.setLicenseCompanyName(bo.getLicenseCompanyName());
        entity.setCompanyType(bo.getCompanyType());
        entity.setLegalRepresentative(bo.getLegalRepresentative());
        entity.setRegisteredCapital(bo.getRegisteredCapital());
        entity.setBuildDate(bo.getBuildDate());
        entity.setOperatingPeriod(bo.getOperatingPeriod());
        entity.setBusinessScope(bo.getBusinessScope());
        entity.setCompanyAddress(bo.getCompanyAddress());
        entity.setAgentPhoneNum(bo.getAgentPhoneNum());
        entity.setPhoneNum(bo.getPhoneNum());
        entity.setApplyType(bo.getApplyType());
        this.baseMapper.insert(entity);
        return entity;
    }

    public int updateEntity(TaxiImportEntity entity) {
        UpdateWrapper<TaxiImportEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(TaxiImportEntity::getNumber, entity.getNumber())
                .eq(TaxiImportEntity::getPlateNo, entity.getPlateNo());
        return this.baseMapper.update(entity, updateWrapper);
    }

    public int updateEntity(TaxiImportEntity entity, Integer id) {
        UpdateWrapper<TaxiImportEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(TaxiImportEntity::getId, id);
        return this.baseMapper.update(entity, updateWrapper);
    }

    public IPage<TaxiImportEntity> pageData(Integer companyId, String plateNo, Integer importStatus, Date startImportTime, Date endImportTime, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<TaxiImportEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotNull(companyId)) {
            queryWrapper.eq(TaxiImportEntity::getCompanyId, companyId);
        }

        if (StringUtils.isNotEmpty(plateNo)) {
            queryWrapper.eq(TaxiImportEntity::getPlateNo, plateNo);
        }

        if (ObjectUtil.isNotNull(importStatus)) {
            queryWrapper.eq(TaxiImportEntity::getImportStatus, importStatus);
        }

        if (ObjectUtil.isNotNull(startImportTime)) {
            queryWrapper.gt(TaxiImportEntity::getCreatedAt, startImportTime);
        }

        if (ObjectUtil.isNotNull(endImportTime)) {
            queryWrapper.lt(TaxiImportEntity::getCreatedAt, endImportTime);
        }

        queryWrapper.orderByDesc(TaxiImportEntity::getCreatedAt);

        if (ObjectUtil.isNull(pageNum)) {
            pageNum = 1;
        }

        if (ObjectUtil.isNull(pageSize)) {
            pageSize = 10;
        }

        return this.baseMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);
    }

    public IPage<TaxiImportEntity> taxiImportPageList(ImportListDto importListDto) {
        LambdaQueryWrapper<TaxiImportEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(importListDto.getPlateNo()), TaxiImportEntity::getPlateNo, importListDto.getPlateNo())
                .eq(ObjectUtil.isNotNull(importListDto.getCompanyId()), TaxiImportEntity::getCompanyId, importListDto.getCompanyId())
                .eq(ObjectUtil.isNotNull(importListDto.getImportStatus()), TaxiImportEntity::getImportStatus, importListDto.getImportStatus())
                .gt(ObjectUtil.isNotNull(importListDto.getStartImportTime()), TaxiImportEntity::getCreatedAt, importListDto.getStartImportTime())
                .le(ObjectUtil.isNotNull(importListDto.getEndImportTime()), TaxiImportEntity::getCreatedAt, importListDto.getEndImportTime()+" 23:59:59")
                .gt(StringUtils.isNotEmpty(importListDto.getActivateTimeStart()),TaxiImportEntity::getActivatedAt,importListDto.getActivateTimeStart())
                .le(StringUtils.isNotEmpty(importListDto.getActivateTimeEnd()),TaxiImportEntity::getActivatedAt,importListDto.getActivateTimeEnd()+" 23:59:59")
                .orderByDesc(TaxiImportEntity::getCreatedAt)
        ;
        return this.baseMapper.selectPage(new Page<>(importListDto.getPageNum(), importListDto.getPageSize()), queryWrapper);
    }

    public TaxiImportEntity getById(Integer id) {
        LambdaQueryWrapper<TaxiImportEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaxiImportEntity::getId, id);
        return this.baseMapper.selectOne(queryWrapper);
    }
}
