package com.ets.apply.application.common.utils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.Base64;


public class FileUtils {

    public static String encrptToBase(String fileUrl) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            InputStream in = conn.getInputStream();
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 对字节数组进行Base64编码，得到Base64编码的字符串
        return Base64.getEncoder().encodeToString(data);
    }

    public static String encrptToBase(File file) {
        byte[] data = null;
        // 读取图片字节数组
        try {
            InputStream in = new FileInputStream(file);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 对字节数组进行Base64编码，得到Base64编码的字符串
        return Base64.getEncoder().encodeToString(data);
    }

    /*
     * @description: 通过url生成，下载文件
     */
    public static String downLoad(String fileUrl,File file) {
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            InputStream in = conn.getInputStream();
            FileOutputStream out = new FileOutputStream(file);
            int j = 0;
            while ((j = in.read()) != -1 ){
                out.write(j);
            }
            in.close();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file.getAbsolutePath() ;
    }

    public static File imageUrlToFile(String imageUrl) {
        File file = null;
        URL urlFile;
        InputStream inputStream = null;
        OutputStream outputStream= null;
        try {
            file = File.createTempFile("tmpImg", ".png");
            //下载
            urlFile = new URL(imageUrl);
            inputStream = urlFile.openStream();
            outputStream= Files.newOutputStream(file.toPath());

            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (Throwable e) {
            e.getStackTrace();
        } finally {
            try {
                if (null != outputStream) {
                    outputStream.close();
                }
                if (null != inputStream) {
                    inputStream.close();
                }
            } catch (Throwable e) {
                e.getStackTrace();
            }
        }
        return file;
    }



    public static void main(String[] args) {
        System.out.println(encrptToBase("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fs9.rr.itc.cn%2Fr%2FwapChange%2F20166_11_1%2Fa6svt50021345074352.jpg&refer=http%3A%2F%2Fs9.rr.itc.cn&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1633831028&t=8c99dc8321f02f68353b2656d09b6633"));
    }
}
