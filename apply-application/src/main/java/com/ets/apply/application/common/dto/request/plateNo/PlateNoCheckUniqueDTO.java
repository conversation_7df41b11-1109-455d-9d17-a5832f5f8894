package com.ets.apply.application.common.dto.request.plateNo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;

@Data
public class PlateNoCheckUniqueDTO {
    @NotNull(message = "合作商户号必填")
    private String companyId;

    @NotNull(message = "车牌号码不能为空")
    private String plateNo;

    @PositiveOrZero(message = "车牌颜色不能为空")
    private Integer plateColor;
}
