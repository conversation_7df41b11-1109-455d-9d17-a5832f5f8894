package com.ets.apply.application.common.consts.productOrder;

import com.ets.apply.application.app.factory.productPartner.impl.*;
import com.ets.common.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReferValueEnum {

    DEFAULT(0, "", DefaultPartnerValue.class, "默认无渠道"),
    PARTNER_VALUE_VASP(1, "vasp", VaspPartnerValue.class,"人寿财险"),
    PARTNER_VALUE_WECAR(2, "wecar", WecarPartnerValue.class,"腾讯出行"),
    PARTNER_VALUE_GEELY(3, "geely", GeelyPartnerValue.class,"吉利"),
    PARTNER_VALUE_XCONNECT(4, "xconnect", XConnectPartnerValue.class,"行致科技埃安"),
    PARTNER_VALUE_CHUANQI(5, "chuanqi", ChuanQiPartnerValue.class,"传祺"),
    PARTNER_VALUE_AMAP(6,"amap",AmapPartnerValue.class,"高德"),
    PARTNER_VALUE_HAOBO(7,"haobo",XConnectPartnerValue.class,"行致科技昊铂"),
    ;

    private final Integer value;
    private final String companyId;
    private final Class<? extends ProductPartnerBase> cls;
    private final String description;


    public static String getCompanyIdByReferValue(Integer value) {
        for (ReferValueEnum referValueEnum : ReferValueEnum.values()) {
            if (referValueEnum.getValue().equals(value)) {
                return referValueEnum.getCompanyId();
            }
        }
        return null;
    }

    public static ReferValueEnum getByValue(Integer value) throws BizException {
        for (ReferValueEnum referValueEnum : ReferValueEnum.values()) {
            if (referValueEnum.getValue().equals(value)) {
                return referValueEnum;
            }
        }
        return null;
    }

    public static ReferValueEnum getByCompanyId(String companyId) {
        for (ReferValueEnum referValueEnum : ReferValueEnum.values()) {
            if (referValueEnum.getCompanyId().equals(companyId)) {
                return referValueEnum;
            }
        }
        return null;
    }
}
