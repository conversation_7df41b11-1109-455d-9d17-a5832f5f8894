package com.ets.apply.application.app.business.productOrder;


import com.ets.apply.application.common.config.InvoiceConfig;
import com.ets.apply.application.common.consts.productOrder.ProductOrderLogTypeEnum;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.vo.productOrder.ProductOrderInvoiceInfoVO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductOrderInvoice;
import com.ets.apply.application.infra.entity.ProductOrderLogEntity;
import com.ets.apply.application.infra.service.ProductOrderInvoiceService;
import com.ets.apply.application.infra.service.ProductOrderLogService;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
public class ProductOrderInvoiceBusiness {

    @Autowired
    private ProductOrderInvoiceService productOrderInvoiceService;

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private InvoiceConfig invoiceConfig;

    @Autowired
    private ProductOrderLogService productOrderLogService;

    /**
     * 获取开票信息
     * @param productOrderSn 商品订单号
     * @return 开票信息
     */
    public ProductOrderInvoiceInfoVO getInvoiceInfo(String productOrderSn) {
        log.info("获取开票信息，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询订单信息
        ProductOrderEntity orderEntity = productOrderService.getByProductOrderSn(productOrderSn);
        if (orderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }

        // 检查是否允许开票
        if (!invoiceConfig.getBackendSourceList().contains(orderEntity.getSource())) {
            ToolsHelper.throwException("该渠道不支持开票");
        }

        // 查询开票记录
        ProductOrderInvoice invoiceEntity = productOrderInvoiceService.getByProductOrderSn(productOrderSn);

        // 构建返回对象
        ProductOrderInvoiceInfoVO vo = new ProductOrderInvoiceInfoVO();

        if (invoiceEntity != null) {
            vo.setInvoiceUrl(invoiceEntity.getInvoiceUrl());
            vo.setInvoiceSn(invoiceEntity.getInvoiceSn());
        }

        return vo;
    }

    /**
     * 开票
     * @param productOrderSn 商品订单号
     * @return 开票信息
     */
    public ProductOrderInvoiceInfoVO invoice(String productOrderSn) {
        log.info("开始开票，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询订单信息
        ProductOrderEntity orderEntity = productOrderService.getByProductOrderSn(productOrderSn);
        if (orderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }

        // 检查是否允许开票
        if (!invoiceConfig.getBackendSourceList().contains(orderEntity.getSource())) {
            ToolsHelper.throwException("该渠道不支持开票");
        }

        // 检查订单状态
        if (!orderEntity.getOrderStatus().equals(ProductOrderStatusEnum.ACTIVATED.getCode())) {
            ToolsHelper.throwException("订单状态不允许开票");
        }

        // 查询是否已有开票记录
        ProductOrderInvoice existInvoice = productOrderInvoiceService.getByProductOrderSn(productOrderSn);

        // TODO: 调用第三方开票接口
        // 这里应该调用实际的开票服务，获取开票链接和流水号
        InvoiceResult invoiceResult = callThirdPartyInvoiceApi(productOrderSn);
        String invoiceUrl = invoiceResult.getInvoiceUrl();
        String invoiceSn = invoiceResult.getInvoiceSn();

        if (existInvoice != null) {
            // 更新现有记录
            existInvoice.setInvoiceUrl(invoiceUrl);
            existInvoice.setInvoiceSn(invoiceSn);
            existInvoice.setUpdatedAt(LocalDateTime.now());
            productOrderInvoiceService.updateById(existInvoice);
            log.info("更新开票记录成功，订单号：{}，开票流水号：{}", productOrderSn, invoiceSn);
        } else {
            // 创建新记录
            ProductOrderInvoice invoiceEntity = new ProductOrderInvoice();
            invoiceEntity.setProductOrderSn(productOrderSn);
            invoiceEntity.setInvoiceUrl(invoiceUrl);
            invoiceEntity.setInvoiceSn(invoiceSn);
            invoiceEntity.setCreatedAt(LocalDateTime.now());
            invoiceEntity.setUpdatedAt(LocalDateTime.now());
            productOrderInvoiceService.save(invoiceEntity);
            log.info("创建开票记录成功，订单号：{}，开票流水号：{}", productOrderSn, invoiceSn);
        }


        // 记录日志
        String logContent = existInvoice != null ?
                "重新开票（开票流水号：" + invoiceSn + "）" :
                "开票成功（开票流水号：" + invoiceSn + "）";
        createInvoiceLog(
                ProductOrderLogTypeEnum.INVOICE.getCode(),
                orderEntity,
                logContent,
                RequestHelper.getAdminOperator()
        );

        // 构建返回对象
        ProductOrderInvoiceInfoVO vo = new ProductOrderInvoiceInfoVO();
        vo.setInvoiceUrl(invoiceUrl);
        vo.setInvoiceSn(invoiceSn);

        return vo;
    }

    /**
     * 开票冲红
     * @param productOrderSn 商品订单号
     */
    public void invoiceReversal(String productOrderSn) {
        log.info("开始开票冲红，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询开票记录
        ProductOrderInvoice invoiceEntity = productOrderInvoiceService.getByProductOrderSn(productOrderSn);

        if (invoiceEntity == null) {
            ToolsHelper.throwException("该订单未开票，无法冲红");
        }

        // TODO: 调用第三方开票冲红接口
        // 这里应该调用实际的开票冲红服务

        // 查询订单信息用于记录日志
        ProductOrderEntity orderEntity = productOrderService.getByProductOrderSn(productOrderSn);

        // 删除开票记录
        productOrderInvoiceService.removeById(invoiceEntity.getId());

        // 记录日志
        createInvoiceLog(
                ProductOrderLogTypeEnum.INVOICE_REVERSAL.getCode(),
                orderEntity,
                "开票冲红（原开票流水号：" + invoiceEntity.getInvoiceSn() + "）",
                RequestHelper.getAdminOperator()
        );

        log.info("开票冲红成功，订单号：{}", productOrderSn);
    }

    /**
     * 发送开票短信
     * @param productOrderSn 商品订单号
     */
    public void invoiceSms(String productOrderSn) {
        log.info("开始发送开票短信，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询开票记录
        ProductOrderInvoice invoiceEntity = productOrderInvoiceService.getByProductOrderSn(productOrderSn);

        if (invoiceEntity == null) {
            ToolsHelper.throwException("该订单未开票，无法发送短信");
        }

        // 查询订单信息获取手机号
        ProductOrderEntity orderEntity = productOrderService.getByProductOrderSn(productOrderSn);
        if (orderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }

        if (StringUtils.isEmpty(orderEntity.getEtcPhone())) {
            ToolsHelper.throwException("订单收货手机号为空，无法发送短信");
        }

        // TODO: 调用短信服务发送开票链接
        // 这里应该调用实际的短信服务
        sendInvoiceSms(orderEntity.getSendPhone(), invoiceEntity.getInvoiceUrl());

        // 记录日志
        createInvoiceLog(
                ProductOrderLogTypeEnum.INVOICE_SMS.getCode(),
                orderEntity,
                "发送开票短信（手机号：" + orderEntity.getSendPhone() + "）",
                RequestHelper.getAdminOperator()
        );

        log.info("开票短信发送成功，订单号：{}，手机号：{}", productOrderSn, orderEntity.getSendPhone());
    }

    /**
     * 调用第三方开票接口（临时实现）
     */
    private InvoiceResult callThirdPartyInvoiceApi(String productOrderSn) {
        // TODO: 实际应该调用第三方开票服务
        InvoiceResult result = new InvoiceResult();
        result.setInvoiceUrl("https://invoice.example.com/invoice?orderSn=" + productOrderSn);
        result.setInvoiceSn("INV" + System.currentTimeMillis());
        return result;
    }

    /**
     * 第三方开票接口返回结果
     */
    private static class InvoiceResult {
        private String invoiceUrl;
        private String invoiceSn;

        public String getInvoiceUrl() {
            return invoiceUrl;
        }

        public void setInvoiceUrl(String invoiceUrl) {
            this.invoiceUrl = invoiceUrl;
        }

        public String getInvoiceSn() {
            return invoiceSn;
        }

        public void setInvoiceSn(String invoiceSn) {
            this.invoiceSn = invoiceSn;
        }
    }

    /**
     * 发送开票短信（临时实现）
     */
    private void sendInvoiceSms(String phone, String invoiceUrl) {
        // TODO: 实际应该调用短信服务
        log.info("发送开票短信到手机：{}，开票链接：{}", phone, invoiceUrl);
    }

    /**
     * 创建开票操作日志
     * @param logType 日志类型
     * @param orderEntity 订单实体
     * @param content 日志内容
     * @param operator 操作员
     */
    private void createInvoiceLog(Integer logType, ProductOrderEntity orderEntity, String content, String operator) {
        try {
            ProductOrderLogEntity logEntity = new ProductOrderLogEntity();
            logEntity.setOperateUser(operator);
            logEntity.setProductOrderSn(orderEntity.getProductOrderSn());
            logEntity.setLogType(logType);
            logEntity.setContent(content);
            logEntity.setAfterStatus(orderEntity.getOrderStatus());
            logEntity.setPreStatus(orderEntity.getOrderStatus());
            logEntity.setLogisticStatus(orderEntity.getLogisticStatus());

            productOrderLogService.create(logEntity);
        } catch (Exception e) {
            log.info("创建开票日志异常：{}", e.getMessage());
        }
    }
}
