package com.ets.apply.application.app.business.productOrder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.common.config.InvoiceConfig;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.common.vo.productOrder.ProductOrderInvoiceInfoVO;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import com.ets.apply.application.infra.entity.ProductOrderInvoice;
import com.ets.apply.application.infra.service.ProductOrderInvoiceService;
import com.ets.apply.application.infra.service.ProductOrderService;
import com.ets.common.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
public class ProductOrderInvoiceBusiness {

    @Autowired
    private ProductOrderInvoiceService productOrderInvoiceService;

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private InvoiceConfig invoiceConfig;

    /**
     * 获取开票信息
     * @param productOrderSn 商品订单号
     * @return 开票信息
     */
    public ProductOrderInvoiceInfoVO getInvoiceInfo(String productOrderSn) {
        log.info("获取开票信息，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询订单信息
        ProductOrderEntity orderEntity = productOrderService.getOneByColumn(productOrderSn, ProductOrderEntity::getProductOrderSn);
        if (orderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }

        // 检查是否允许开票
        if (!invoiceConfig.getBackendSourceList().contains(orderEntity.getSource())) {
            ToolsHelper.throwException("该渠道不支持开票");
        }

        // 查询开票记录
        LambdaQueryWrapper<ProductOrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderInvoice::getProductOrderSn, productOrderSn);
        ProductOrderInvoice invoiceEntity = productOrderInvoiceService.getOne(wrapper);

        // 构建返回对象
        ProductOrderInvoiceInfoVO vo = new ProductOrderInvoiceInfoVO();

        if (invoiceEntity != null) {
            vo.setInvoiceUrl(invoiceEntity.getInvoiceUrl());
            vo.setInvoiceSn(invoiceEntity.getInvoiceSn());
        }

        return vo;
    }

    /**
     * 开票
     * @param productOrderSn 商品订单号
     * @return 开票信息
     */
    public ProductOrderInvoiceInfoVO invoice(String productOrderSn) {
        log.info("开始开票，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询订单信息
        ProductOrderEntity orderEntity = productOrderService.getOneByColumn(productOrderSn, ProductOrderEntity::getProductOrderSn);
        if (orderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }

        // 检查是否允许开票
        if (!invoiceConfig.getBackendSourceList().contains(orderEntity.getSource())) {
            ToolsHelper.throwException("该渠道不支持开票");
        }

        // 检查订单状态
        if (!orderEntity.getOrderStatus().equals(ProductOrderStatusEnum.ACTIVATED.getCode())) {
            ToolsHelper.throwException("订单状态不允许开票");
        }

        // 查询是否已有开票记录
        LambdaQueryWrapper<ProductOrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderInvoice::getProductOrderSn, productOrderSn);
        ProductOrderInvoice existInvoice = productOrderInvoiceService.getOne(wrapper);

        // TODO: 调用第三方开票接口
        // 这里应该调用实际的开票服务，获取开票链接和流水号
        String invoiceUrl = generateInvoiceUrl(productOrderSn);
        String invoiceSn = generateInvoiceSn();

        if (existInvoice != null) {
            // 更新现有记录
            existInvoice.setInvoiceUrl(invoiceUrl);
            existInvoice.setInvoiceSn(invoiceSn);
            existInvoice.setUpdatedAt(LocalDateTime.now());
            productOrderInvoiceService.updateById(existInvoice);
            log.info("更新开票记录成功，订单号：{}，开票流水号：{}", productOrderSn, invoiceSn);
        } else {
            // 创建新记录
            ProductOrderInvoice invoiceEntity = new ProductOrderInvoice();
            invoiceEntity.setProductOrderSn(productOrderSn);
            invoiceEntity.setInvoiceUrl(invoiceUrl);
            invoiceEntity.setInvoiceSn(invoiceSn);
            invoiceEntity.setCreatedAt(LocalDateTime.now());
            invoiceEntity.setUpdatedAt(LocalDateTime.now());
            productOrderInvoiceService.save(invoiceEntity);
            log.info("创建开票记录成功，订单号：{}，开票流水号：{}", productOrderSn, invoiceSn);
        }


        // 构建返回对象
        ProductOrderInvoiceInfoVO vo = new ProductOrderInvoiceInfoVO();
        vo.setInvoiceUrl(invoiceUrl);
        vo.setInvoiceSn(invoiceSn);

        return vo;
    }

    /**
     * 开票冲红
     * @param productOrderSn 商品订单号
     */
    public void invoiceReversal(String productOrderSn) {
        log.info("开始开票冲红，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询开票记录
        LambdaQueryWrapper<ProductOrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderInvoice::getProductOrderSn, productOrderSn);
        ProductOrderInvoice invoiceEntity = productOrderInvoiceService.getOne(wrapper);

        if (invoiceEntity == null) {
            ToolsHelper.throwException("该订单未开票，无法冲红");
        }

        // TODO: 调用第三方开票冲红接口
        // 这里应该调用实际的开票冲红服务

        // 删除开票记录
        productOrderInvoiceService.removeById(invoiceEntity.getId());

        log.info("开票冲红成功，订单号：{}", productOrderSn);
    }

    /**
     * 发送开票短信
     * @param productOrderSn 商品订单号
     */
    public void invoiceSms(String productOrderSn) {
        log.info("开始发送开票短信，订单号：{}", productOrderSn);

        if (StringUtils.isEmpty(productOrderSn)) {
            ToolsHelper.throwException("订单号不能为空");
        }

        // 查询开票记录
        LambdaQueryWrapper<ProductOrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderInvoice::getProductOrderSn, productOrderSn);
        ProductOrderInvoice invoiceEntity = productOrderInvoiceService.getOne(wrapper);

        if (invoiceEntity == null) {
            ToolsHelper.throwException("该订单未开票，无法发送短信");
        }

        // 查询订单信息获取手机号
        ProductOrderEntity orderEntity = productOrderService.getOneByColumn(productOrderSn, ProductOrderEntity::getProductOrderSn);
        if (orderEntity == null) {
            ToolsHelper.throwException("订单不存在");
        }

        if (StringUtils.isEmpty(orderEntity.getSendPhone())) {
            ToolsHelper.throwException("订单收货手机号为空，无法发送短信");
        }

        // TODO: 调用短信服务发送开票链接
        // 这里应该调用实际的短信服务
        sendInvoiceSms(orderEntity.getSendPhone(), invoiceEntity.getInvoiceUrl());

        log.info("开票短信发送成功，订单号：{}，手机号：{}", productOrderSn, orderEntity.getSendPhone());
    }

    /**
     * 生成开票链接（临时实现）
     */
    private String generateInvoiceUrl(String productOrderSn) {
        // TODO: 实际应该调用第三方开票服务生成真实链接
        return "https://invoice.example.com/invoice?orderSn=" + productOrderSn;
    }

    /**
     * 生成开票流水号（临时实现）
     */
    private String generateInvoiceSn() {
        // TODO: 实际应该使用真实的流水号生成规则
        return "INV" + System.currentTimeMillis();
    }

    /**
     * 发送开票短信（临时实现）
     */
    private void sendInvoiceSms(String phone, String invoiceUrl) {
        // TODO: 实际应该调用短信服务
        log.info("发送开票短信到手机：{}，开票链接：{}", phone, invoiceUrl);
    }
}
