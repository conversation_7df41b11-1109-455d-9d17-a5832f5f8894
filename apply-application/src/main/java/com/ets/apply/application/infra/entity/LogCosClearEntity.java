package com.ets.apply.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Getter
@Setter
@TableName("log_cos_clear")
public class LogCosClearEntity extends BaseEntity<LogCosClearEntity> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型
     */
    private String cosCategory;

    /**
     * 时间
     */
    private String cosDate;

    /**
     * 文件名
     */
    private String cosKey;

    /**
     * 是否匹配到
     */
    private Boolean matchStatus;

    /**
     * 是否已清除
     */
    private Boolean delStatus;

    /**
     * 来源，默认空为存储桶
     */
    private String source;

    /**
     * 用id来处理数据
     */
    private Integer sourceId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
