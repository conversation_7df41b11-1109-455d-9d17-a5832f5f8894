package com.ets.apply.application.app.factory.productPartner.impl;

import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.service.thirdPartner.XconnectService;
import com.ets.apply.application.common.bo.productOrder.ProductOrderRefundBO;
import com.ets.apply.application.common.bo.productOrder.ProductOrderThirdSyncBO;
import com.ets.apply.application.common.config.queue.PayGatewayConfig;
import com.ets.apply.application.common.consts.productOrder.ProductOrderStatusEnum;
import com.ets.apply.application.infra.entity.ProductOrderEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class XConnectPartnerValue extends ProductPartnerBase {

    @Autowired
    protected PayGatewayConfig payGatewayConfig;

    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Autowired
    private XconnectService xconnectService;
    /**
     * 退款操作发起
     * @param productOrderRefundBO
     * @return
     */
    @Override
    public String refund(ProductOrderRefundBO productOrderRefundBO) {
        productOrderBusiness.thirdOrderRefund(productOrderRefundBO);
        return "";
    }
    /*
     * 订单状态同步
     */
    public  void orderSync(ProductOrderEntity productOrder, ProductOrderThirdSyncBO bo){
        //新增，修改
        if(bo.getStatus().equals(ProductOrderStatusEnum.DEFAULT.getCode())){
            xconnectService.etcOrderSave(productOrder);
        }else{
            xconnectService.etcOrderUpdate(productOrder);
        }
    }

}
