package com.ets.apply.application.infra.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.common.vo.adminStatistic.StatisticCountByStatusVO;
import com.ets.apply.application.infra.entity.ActivityCreditCardBankUsersEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
@Repository
@DS("db-etc-proxy")
public interface ActivityCreditCardBankUsersMapper extends CommonBaseMapper<ActivityCreditCardBankUsersEntity> {
    @Select("select count(*) as sum,status from etc_activity_credit_card_bank_users where which_bank=#{whichBank} group by status")
    List<StatisticCountByStatusVO> getCountByWhichBank(@Param("whichBank") Integer whichBank);

    @Select("select count(*) as sum,status from etc_activity_credit_card_bank_users where which_bank=#{whichBank} and created_at>#{createdAt} group by status")
    List<StatisticCountByStatusVO> getCountByWhichBankAndCreatedAt(@Param("whichBank") Integer whichBank, @Param("createdAt") LocalDateTime createdAt);

    /* 进件+进件时间*/
    @Select("select count(*) as sum from etc_activity_credit_card_bank_users where which_bank=#{whichBank} and apply_completed=1 and submit_time>=#{submitTime}")
    StatisticCountByStatusVO getCountByWhichBankAndSubmitTime(@Param("whichBank") Integer whichBank, @Param("submitTime") LocalDateTime submitTime);
    /* 审核通过+审核时间*/
    @Select("select count(*) as sum from etc_activity_credit_card_bank_users where which_bank=#{whichBank} and status=3 and audit_time>=#{auditTime}")
    StatisticCountByStatusVO getCountByWhichBankAndAuditTime(@Param("whichBank") Integer whichBank, @Param("auditTime") LocalDateTime auditTime);
}
