package com.ets.apply.application.app.thirdservice.feign;

import com.ets.apply.application.app.thirdservice.fallback.PayGatewayFallbackFactory;
import com.ets.apply.application.common.bo.pay.PrepayParamBO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 支付接口调用类
 */
@FeignClient(url = "${params.microUrls.pay-gateway}", name = "PayGatewayFeign", fallbackFactory = PayGatewayFallbackFactory.class)
public interface PayGatewayFeign {

    @PostMapping(
            value = "/payment/release/unified/pay"
    )
    String prepay(@RequestBody PrepayParamBO params);



}
