package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ets.apply.application.infra.entity.ProductOrderInvoice;
import com.ets.apply.application.infra.mapper.ProductOrderInvoiceMapper;
import com.ets.apply.application.infra.service.BaseService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-28
 */
@Service
@DS("db-etc-apply")
public class ProductOrderInvoiceService extends BaseService<ProductOrderInvoiceMapper, ProductOrderInvoice> {

}
