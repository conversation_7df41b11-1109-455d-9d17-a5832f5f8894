package com.ets.apply.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.apply.application.infra.entity.ProductOrderInvoice;
import com.ets.apply.application.infra.mapper.ProductOrderInvoiceMapper;
import com.ets.apply.application.infra.service.BaseService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-28
 */
@Service
@DS("db-etc-apply")
public class ProductOrderInvoiceService extends BaseService<ProductOrderInvoiceMapper, ProductOrderInvoice> {

    /**
     * 根据商品订单号查询开票记录
     * @param productOrderSn 商品订单号
     * @return 开票记录
     */
    public ProductOrderInvoice getByProductOrderSn(String productOrderSn) {
        LambdaQueryWrapper<ProductOrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderInvoice::getProductOrderSn, productOrderSn);
        return getOne(wrapper);
    }
}
