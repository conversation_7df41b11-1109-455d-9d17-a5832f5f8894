package com.ets.apply.application;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "test")
public class CreditCardUpdatedDayTest {

    @Test
    public void testCalculateUpdatedDays() {
        LocalDateTime now = LocalDateTime.now();
        
        // 测试今天的时间
        LocalDateTime today = now;
        long daysToday = ChronoUnit.DAYS.between(today.toLocalDate(), now.toLocalDate());
        Assert.assertEquals("今天应该是0天", 0, daysToday);
        
        // 测试昨天的时间
        LocalDateTime yesterday = now.minusDays(1);
        long daysYesterday = ChronoUnit.DAYS.between(yesterday.toLocalDate(), now.toLocalDate());
        Assert.assertEquals("昨天应该是1天", 1, daysYesterday);
        
        // 测试一周前的时间
        LocalDateTime oneWeekAgo = now.minusDays(7);
        long daysWeek = ChronoUnit.DAYS.between(oneWeekAgo.toLocalDate(), now.toLocalDate());
        Assert.assertEquals("一周前应该是7天", 7, daysWeek);
        
        // 测试一个月前的时间
        LocalDateTime oneMonthAgo = now.minusDays(30);
        long daysMonth = ChronoUnit.DAYS.between(oneMonthAgo.toLocalDate(), now.toLocalDate());
        Assert.assertEquals("30天前应该是30天", 30, daysMonth);
        
        // 测试未来时间（应该是负数，但我们的业务逻辑可能需要处理）
        LocalDateTime tomorrow = now.plusDays(1);
        long daysTomorrow = ChronoUnit.DAYS.between(tomorrow.toLocalDate(), now.toLocalDate());
        Assert.assertEquals("明天应该是-1天", -1, daysTomorrow);
        
        System.out.println("✅ 时间计算测试通过");
        System.out.println("今天: " + daysToday + " 天");
        System.out.println("昨天: " + daysYesterday + " 天");
        System.out.println("一周前: " + daysWeek + " 天");
        System.out.println("30天前: " + daysMonth + " 天");
        System.out.println("明天: " + daysTomorrow + " 天");
    }
    
    @Test
    public void testCalculateUpdatedDaysWithTime() {
        LocalDateTime now = LocalDateTime.now();
        
        // 测试同一天但不同时间的情况
        LocalDateTime morningToday = now.withHour(8).withMinute(0).withSecond(0);
        LocalDateTime eveningToday = now.withHour(20).withMinute(0).withSecond(0);
        
        long daysMorning = ChronoUnit.DAYS.between(morningToday.toLocalDate(), now.toLocalDate());
        long daysEvening = ChronoUnit.DAYS.between(eveningToday.toLocalDate(), now.toLocalDate());
        
        Assert.assertEquals("同一天早上应该是0天", 0, daysMorning);
        Assert.assertEquals("同一天晚上应该是0天", 0, daysEvening);
        
        // 测试跨天的边界情况
        LocalDateTime yesterdayEvening = now.minusDays(1).withHour(23).withMinute(59).withSecond(59);
        LocalDateTime todayMorning = now.withHour(0).withMinute(0).withSecond(1);
        
        long daysYesterdayEvening = ChronoUnit.DAYS.between(yesterdayEvening.toLocalDate(), now.toLocalDate());
        long daysTodayMorning = ChronoUnit.DAYS.between(todayMorning.toLocalDate(), now.toLocalDate());
        
        Assert.assertEquals("昨天晚上应该是1天", 1, daysYesterdayEvening);
        Assert.assertEquals("今天凌晨应该是0天", 0, daysTodayMorning);
        
        System.out.println("✅ 跨天边界测试通过");
    }
    
    @Test
    public void testNullUpdatedAt() {
        // 测试 updatedAt 为 null 的情况
        LocalDateTime updatedAt = null;
        int updatedDay;
        
        if (updatedAt != null) {
            LocalDateTime now = LocalDateTime.now();
            long daysBetween = ChronoUnit.DAYS.between(updatedAt.toLocalDate(), now.toLocalDate());
            updatedDay = (int) daysBetween;
        } else {
            updatedDay = 0;
        }
        
        Assert.assertEquals("null时间应该返回0天", 0, updatedDay);
        System.out.println("✅ null值处理测试通过");
    }
    
    @Test
    public void testBusinessLogicSimulation() {
        // 模拟业务逻辑
        LocalDateTime now = LocalDateTime.now();

        // 模拟不同的更新时间场景
        LocalDateTime[] testTimes = {
            now,                    // 今天
            now.minusDays(1),      // 昨天
            now.minusDays(3),      // 3天前
            now.minusDays(7),      // 一周前
            now.minusDays(15),     // 半个月前
            now.minusDays(30),     // 一个月前
            null                   // null值
        };

        String[] descriptions = {
            "今天更新", "昨天更新", "3天前更新",
            "一周前更新", "半个月前更新", "一个月前更新", "从未更新"
        };

        System.out.println("业务场景测试：");
        for (int i = 0; i < testTimes.length; i++) {
            LocalDateTime updatedAt = testTimes[i];
            int updatedDay;

            if (updatedAt != null) {
                long daysBetween = ChronoUnit.DAYS.between(updatedAt.toLocalDate(), now.toLocalDate());
                updatedDay = (int) daysBetween;
            } else {
                updatedDay = 0;
            }

            System.out.println(descriptions[i] + ": " + updatedDay + " 天");

            // 验证结果合理性
            if (i < testTimes.length - 1) { // 排除null的情况
                Assert.assertTrue(descriptions[i] + "天数应该>=0", updatedDay >= 0);
            }
        }

        System.out.println("✅ 业务逻辑模拟测试通过");
    }

    @Test
    public void testRecentUpdateTimeLogic() {
        LocalDateTime now = LocalDateTime.now();

        // 测试两个时间都不为null的情况
        LocalDateTime usersInfoTime = now.minusDays(5);
        LocalDateTime bankUsersTime = now.minusDays(3);

        LocalDateTime recentTime = usersInfoTime.isAfter(bankUsersTime) ? usersInfoTime : bankUsersTime;
        Assert.assertEquals("应该选择较新的时间", bankUsersTime, recentTime);

        // 测试一个为null的情况
        LocalDateTime nullTime = null;
        LocalDateTime nonNullTime = now.minusDays(2);

        LocalDateTime result;
        if (nullTime != null && nonNullTime != null) {
            result = nullTime.isAfter(nonNullTime) ? nullTime : nonNullTime;
        } else if (nullTime != null) {
            result = nullTime;
        } else if (nonNullTime != null) {
            result = nonNullTime;
        } else {
            result = null;
        }

        Assert.assertEquals("应该选择非null的时间", nonNullTime, result);

        System.out.println("✅ 最新更新时间逻辑测试通过");
    }
}
