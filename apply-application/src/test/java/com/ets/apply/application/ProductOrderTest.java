package com.ets.apply.application;

import com.ets.apply.application.app.business.ChannelProductOrderBusiness;
import com.ets.apply.application.app.business.ProductOrderBusiness;
import com.ets.apply.application.app.service.thirdPartner.DongFengService;
import com.ets.apply.application.common.config.DongFengConfig;
import com.ets.apply.application.common.dto.request.productOrder.ProductOrderSaleAfterDTO;
import com.ets.common.BizException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class ProductOrderTest {

    @Autowired
    ChannelProductOrderBusiness channelProductOrderBusiness;
    @Autowired
    ProductOrderBusiness productOrderBusiness;

    @Autowired
    private DongFengService dongFengService;

    @Autowired
    private DongFengConfig dongFengConfig;
    @Test
    public void payCallBack() throws BizException {
        String data = "{\"amount\":0.01,\"cardType\":\"\",\"contractCode\":\"\",\"couponFee\":0.00,\"couponId\":\"\",\"debtStatus\":0,\"discountAmount\":0.00,\"errCode\":\"\",\"errMsg\":\"订单已关闭\",\"goodsTag\":\"\",\"mchType\":33,\"openId\":\"oo4Ia60PPhwiQNSg3iZ0sPF3TErM\",\"orderNo\":\"2506091747000001941\",\"payWay\":28,\"paymentNo\":\"14222208040900000025\",\"paymentType\":3,\"provinceId\":0,\"serviceAmount\":0.00,\"status\":2,\"type\":42,\"userId\":1991826815}";
        channelProductOrderBusiness.PaySuccess(data);
    }

    @Test
    public void refundCallBack() throws BizException {
        String data = "{\"amount\":0.01,\"createTime\":\"2022-08-04T19:08:35\",\"errCode\":\"\",\"errMsg\":\"\",\"manual\":1,\"orderNo\":\"2208041433000000551\",\"outRefundNo\":\"50300402592022080423408089992\",\"outTradeNo\":\"4200001561202208041998267870\",\"payAmount\":0.01,\"payWay\":28,\"paymentNo\":\"14222208041400000028\",\"paymentType\":3,\"reason\":\"test\",\"refundNo\":\"19922208041900000035\",\"refundRecvAccout\":\"支付用户零钱\",\"refundTime\":\"2022-08-04T19:09:12\",\"status\":3,\"type\":42,\"userId\":1991826815}";
        channelProductOrderBusiness.refundComplete(data);
    }

    /**
     * 测试取消订单
     */
    @Test
    public void cancel() {
        String productOrderSn = "2507161528000063702";
        productOrderBusiness.cancel(productOrderSn, "取消订单", 1,"admin");
    }


    /**
     * 测试售后
     */
    @Test
    public void afterSale() {
        ProductOrderSaleAfterDTO productOrderSaleAfterDTO = new ProductOrderSaleAfterDTO();
        productOrderSaleAfterDTO.setProductOrderSn("2211291439000000531");
        productOrderSaleAfterDTO.setIsRefund(false);
        productOrderBusiness.productOrderSalesRefund(productOrderSaleAfterDTO);
    }

    @Test
    public void addressTest() {
        String address = "江苏苏州市张家港市锦丰镇华山路550号御景花园1期1幢张家港市";
        String area = productOrderBusiness.getAreaByAddress(address);

        productOrderBusiness.addressAndAreaValidation(address, area);
        String[] split = area.split(" ");
        String last  = split[split.length-1];
        System.out.println(last);

        String[] split1 = address.split(last);
        System.out.println(Arrays.toString(split1));
        String last2 = split1[split1.length-1];

        System.out.println(last2);

        System.out.println(address.endsWith(last2));

    }

    @Test
    public void dongFengNotifyTest(){
        HashMap<String,Object> notifyBody = new HashMap<>();
        notifyBody.put("bizExt","2208041433000000551");
        notifyBody.put("applyProductId","2208041433000000551");
        notifyBody.put("applyProductName","2208041433000000551");
        notifyBody.put("productOrderSn","2208041433000000551");
        notifyBody.put("status",3);
        notifyBody.put("payTime",1718345694554L);

        dongFengService.sendNotify(notifyBody,dongFengConfig.getOrderNotifyApi());

    }
}
