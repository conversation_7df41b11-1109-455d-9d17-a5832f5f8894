package com.ets.apply.application;

import com.ets.apply.application.app.business.productOrder.ProductOrderInvoiceBusiness;
import com.ets.apply.application.common.vo.productOrder.ProductOrderInvoiceInfoVO;
import com.ets.common.BizException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

@SpringBootTest(classes = ApplyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class ProductOrderInvoiceTest {

    @Autowired
    private ProductOrderInvoiceBusiness productOrderInvoiceBusiness;

    /**
     * 测试获取开票信息 - 正常情况
     */
    @Test
    public void testGetInvoiceInfo_Success() {
        // 使用一个存在的订单号进行测试
        String productOrderSn = "TEST_ORDER_001";
        
        try {
            ProductOrderInvoiceInfoVO result = productOrderInvoiceBusiness.getInvoiceInfo(productOrderSn);
            assertNotNull("开票信息不应为空", result);
            assertEquals("订单号应该匹配", productOrderSn, result.getProductOrderSn());
            assertNotNull("是否已开票字段不应为空", result.getHasInvoice());
            assertNotNull("是否允许开票字段不应为空", result.getAllowInvoice());
        } catch (BizException e) {
            // 如果订单不存在，这是预期的异常
            assertTrue("应该抛出订单不存在的异常", e.getMessage().contains("订单不存在"));
        }
    }

    /**
     * 测试获取开票信息 - 订单号为空
     */
    @Test
    public void testGetInvoiceInfo_EmptyOrderSn() {
        try {
            productOrderInvoiceBusiness.getInvoiceInfo("");
            fail("应该抛出订单号不能为空的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单号不能为空", e.getMessage().contains("订单号不能为空"));
        }
    }

    /**
     * 测试获取开票信息 - 订单号为null
     */
    @Test
    public void testGetInvoiceInfo_NullOrderSn() {
        try {
            productOrderInvoiceBusiness.getInvoiceInfo(null);
            fail("应该抛出订单号不能为空的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单号不能为空", e.getMessage().contains("订单号不能为空"));
        }
    }

    /**
     * 测试开票 - 订单号为空
     */
    @Test
    public void testInvoice_EmptyOrderSn() {
        try {
            productOrderInvoiceBusiness.invoice("");
            fail("应该抛出订单号不能为空的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单号不能为空", e.getMessage().contains("订单号不能为空"));
        }
    }

    /**
     * 测试开票冲红 - 订单号为空
     */
    @Test
    public void testInvoiceReversal_EmptyOrderSn() {
        try {
            productOrderInvoiceBusiness.invoiceReversal("");
            fail("应该抛出订单号不能为空的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单号不能为空", e.getMessage().contains("订单号不能为空"));
        }
    }

    /**
     * 测试发送开票短信 - 订单号为空
     */
    @Test
    public void testInvoiceSms_EmptyOrderSn() {
        try {
            productOrderInvoiceBusiness.invoiceSms("");
            fail("应该抛出订单号不能为空的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单号不能为空", e.getMessage().contains("订单号不能为空"));
        }
    }

    /**
     * 测试开票 - 不存在的订单
     */
    @Test
    public void testInvoice_NonExistentOrder() {
        String nonExistentOrderSn = "NON_EXISTENT_ORDER_" + System.currentTimeMillis();
        
        try {
            productOrderInvoiceBusiness.invoice(nonExistentOrderSn);
            fail("应该抛出订单不存在的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单不存在", e.getMessage().contains("订单不存在"));
        }
    }

    /**
     * 测试开票冲红 - 不存在的订单
     */
    @Test
    public void testInvoiceReversal_NonExistentOrder() {
        String nonExistentOrderSn = "NON_EXISTENT_ORDER_" + System.currentTimeMillis();
        
        try {
            productOrderInvoiceBusiness.invoiceReversal(nonExistentOrderSn);
            fail("应该抛出订单未开票的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单未开票", e.getMessage().contains("订单未开票"));
        }
    }

    /**
     * 测试发送开票短信 - 不存在的订单
     */
    @Test
    public void testInvoiceSms_NonExistentOrder() {
        String nonExistentOrderSn = "NON_EXISTENT_ORDER_" + System.currentTimeMillis();
        
        try {
            productOrderInvoiceBusiness.invoiceSms(nonExistentOrderSn);
            fail("应该抛出订单未开票的异常");
        } catch (BizException e) {
            assertTrue("异常信息应该包含订单未开票", e.getMessage().contains("订单未开票"));
        }
    }
}
