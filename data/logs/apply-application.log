{"sysTime":"2025-09-28 11:22:53.246","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-09-28 11:22:53.239","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:53.284","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:53.422","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:53.423","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-09-28 11:22:53.423","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-09-28 11:22:53.423","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-09-28 11:22:53.438","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-09-28 11:22:53.443","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-09-28 11:22:53.498","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:53.508","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-09-28 11:22:53.520","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-09-28 11:22:55.070","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:55.075","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=apply-mysql.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:55.077","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=apply-mysql.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-mysql.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:55.078","level":"INFO","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-09-28 11:22:55.079","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=apply-mysql.yaml, group=apply, tenant=test, config=spring:\\n  autoconfigure:\\n    #自动化配置 例外处理\\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.Dr...\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:56.289","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:56.291","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=common-starter-config.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:56.291","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=common-starter-config.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:56.292","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=common-starter-config.yaml, group=apply, tenant=test, config=spring:\\n  redis:\\n    host: ************\\n    password: Qa@123yes\\n    port: 6379\\n    database: 15\\n  da...\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:57.498","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:57.500","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=apply-cos-tencent.properties, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:57.501","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=apply-cos-tencent.properties, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:57.502","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=apply-cos-tencent.properties, group=apply, tenant=test, config=cos.tencent.secretId=AKIDmJZYHkKuc2D7k6i1dy1pLccLtEI5GfZ0\\ncos.tencent.secretKey=JMCl3wax3wZy7yDKAoCK...\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:58.719","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=big-data-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=big-data-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:58.722","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=big-data-config.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=big-data-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=big-data-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:58.723","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=big-data-config.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=big-data-config.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:58.725","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=big-data-config.yaml, group=apply, tenant=test, config=baseData:\\n  hayama:\\n    driverLicense:\\n      accountId: gdwx_main_test\\n      secret: 4953555a4f56553...\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:59.932","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=template-msg-ids.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=template-msg-ids.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:59.933","level":"ERROR","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=template-msg-ids.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=template-msg-ids.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=template-msg-ids.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-28 11:22:59.933","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=template-msg-ids.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=template-msg-ids.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-28 11:22:59.935","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=template-msg-ids.yaml, group=apply, tenant=test, config=template-msg-ids:\\n  baidu:\\n    tpl-ids-map:\\n      order_ship: dd37b7f64b4d4e7594c3a24c5fb1e065\\n\\n  we...\"}","thrown":""}
{"sysTime":"2025-09-28 11:23:00.157","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-09-28 11:23:00.158","level":"WARN","ip":"127.0.0.1","appName":"apply-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"2506","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
